#%%
%load_ext autoreload
%autoreload 2
#%%
import os
import sys

sys.path.append('..')
#%%

from src.utils.extract_data import get_klines_df

from src.utils.misc import parallel_execute
from src.config.base import INTERVAL_LIMIT_MAPPING
#%%

#%%
def collect_data_task(ticker: str):
    functions_with_kwargs = [
        (get_klines_df, dict(symbol=ticker, interval="1m", limit=INTERVAL_LIMIT_MAPPING["1m"] + 1)),
        (get_klines_df, dict(symbol=ticker, interval="15m", limit=INTERVAL_LIMIT_MAPPING["15m"] + 1)),
        (get_klines_df, dict(symbol=ticker, interval="1h", limit=INTERVAL_LIMIT_MAPPING["1h"] + 1)),
        (get_klines_df, dict(symbol=ticker, interval="4h", limit=INTERVAL_LIMIT_MAPPING["4h"] + 1)),
        (get_klines_df, dict(symbol=ticker, interval="1d", limit=INTERVAL_LIMIT_MAPPING["1d"] + 1))
    ]
    results = parallel_execute(functions_with_kwargs)
    data_dfs = dict(zip([
        'klines_1m_df', 'klines_15m_df', 'klines_1h_df', 'klines_4h_df',
        'klines_1d_df'
    ], results))
    return data_dfs
#%%

#%%

#%%
