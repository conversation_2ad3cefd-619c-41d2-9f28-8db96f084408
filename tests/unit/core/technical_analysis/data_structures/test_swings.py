import datetime
import pandas as pd

from src.core.technical_analysis.data_structures.swings import Swing, Swing<PERSON>igh, SwingLow


class TestSwings:
    """Test cases for the Swing, SwingHigh, and SwingLow classes."""

    def test_swing_high_creation(self):
        """Test creating a SwingHigh directly."""
        dt = datetime.datetime(2021, 12, 1, 13, 20, 0)
        swing = SwingHigh(dt=dt, value=50000.0)

        assert isinstance(swing, SwingHigh)
        assert isinstance(swing, Swing)
        assert swing.dt == dt
        assert swing.value == 50000.0
        assert swing.stype == "high"

    def test_swing_low_creation(self):
        """Test creating a SwingLow directly."""
        dt = datetime.datetime(2021, 12, 1, 13, 20, 0)
        swing = SwingLow(dt=dt, value=49000.0)

        assert isinstance(swing, SwingLow)
        assert isinstance(swing, Swing)
        assert swing.dt == dt
        assert swing.value == 49000.0
        assert swing.stype == "low"

    def test_factory_swing_high(self):
        """Test creating a SwingHigh via the factory method."""
        dt = datetime.datetime(2021, 12, 1, 13, 20, 0)
        swing = Swing(dt=dt, value=50000.0, stype="high")

        assert isinstance(swing, SwingHigh)
        assert swing.dt == dt
        assert swing.value == 50000.0
        assert swing.stype == "high"

    def test_factory_swing_low(self):
        """Test creating a SwingLow via the factory method."""
        dt = datetime.datetime(2021, 12, 1, 13, 20, 0)
        swing = Swing(dt=dt, value=49000.0, stype="low")

        assert isinstance(swing, SwingLow)
        assert swing.dt == dt
        assert swing.value == 49000.0
        assert swing.stype == "low"

    def test_factory_positional_args(self):
        """Test factory with positional arguments."""
        dt = datetime.datetime(2021, 12, 1, 13, 20, 0)
        swing = Swing(dt, 50000.0, "high")

        assert isinstance(swing, SwingHigh)
        assert swing.dt == dt
        assert swing.value == 50000.0
        assert swing.stype == "high"

    def test_pandas_timestamp_conversion(self):
        """Test that pandas Timestamp objects are converted to datetime."""
        pd_timestamp = pd.Timestamp("2021-12-01 13:20:00")
        swing = SwingHigh(dt=pd_timestamp, value=50000.0)

        assert isinstance(swing.dt, datetime.datetime)
        assert swing.dt.year == 2021
        assert swing.dt.month == 12
        assert swing.dt.day == 1
        assert swing.dt.hour == 13
        assert swing.dt.minute == 20

    def test_string_datetime_conversion(self):
        """Test that ISO formatted string dates are converted to datetime."""
        dt_string = "2021-12-01T13:20:00"
        swing = SwingLow(dt=dt_string, value=49000.0)

        assert isinstance(swing.dt, datetime.datetime)
        assert swing.dt.year == 2021
        assert swing.dt.month == 12
        assert swing.dt.day == 1
        assert swing.dt.hour == 13
        assert swing.dt.minute == 20

    def test_default_swing_type(self):
        """Test that a default swing type is used when not specified."""
        # When creating a base Swing without a stype
        swing = Swing(datetime.datetime(2021, 12, 1, 13, 20, 0), 50000.0)

        # It should remain a base Swing with the default stype
        assert type(swing) is Swing
        assert swing.stype == "neutral"  # Default value in the metaclass

    def test_invalid_swing_type(self):
        """Test behavior with an invalid swing type."""
        dt = datetime.datetime(2021, 12, 1, 13, 20, 0)
        swing = Swing(dt=dt, value=50000.0, stype="invalid")

        # Should stay as base Swing class since the factory didn't recognize the type
        assert type(swing) is Swing
        assert swing.stype == "invalid"

    def test_swing_high_stype_override(self):
        """Test that attempting to override the stype in SwingHigh doesn't work."""
        # Try to create a SwingHigh with stype='low'
        dt = datetime.datetime(2021, 12, 1, 13, 20, 0)
        swing = SwingHigh(dt=dt, value=50000.0)

        # The stype should still be 'high' due to the init=False in the field
        assert swing.stype == "high"

    def test_swing_low_stype_override(self):
        """Test that attempting to override the stype in SwingLow doesn't work."""
        # Try to create a SwingLow with stype='high'
        dt = datetime.datetime(2021, 12, 1, 13, 20, 0)
        swing = SwingLow(dt=dt, value=49000.0)

        # The stype should still be 'low' due to the init=False in the field
        assert swing.stype == "low"