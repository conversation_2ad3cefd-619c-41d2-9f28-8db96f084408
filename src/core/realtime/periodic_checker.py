# import logging
# from src.utils.extract_data import get_klines_df
# from src.core.technical_analysis.indicators import add_indicators
# from src.core.realtime.kafka_producer import KafkaProducer
# import time
#
# logging.basicConfig(level=logging.INFO)
#
# SYMBOL = 'SOLUSDT'
# INTERVAL = '1m'
# LIMIT = 100
# KAFKA_TOPIC = 'signals'
#
# INDICATORS_CONFIG = {
#     'rsi': True,
#     'macd': True,
#     # Add more indicators if needed
# }
#
# producer = KafkaProducer(topic=KAFKA_TOPIC)
#
# def check_rules(df):
#     alerts = []
#
#     latest_rsi = df['rsi'].iloc[-1]
#     latest_macd = df['macd'].iloc[-1]
#     latest_macd_signal = df['macd_signal'].iloc[-1]
#
#     # RSI rules
#     if latest_rsi < 30:
#         alerts.append({
#             'type': 'rsi_oversold',
#             'symbol': SYMBOL,
#             'interval': INTERVAL,
#             'rsi': round(latest_rsi, 2),
#             'close_price': df['close_price'].iloc[-1]
#         })
#     elif latest_rsi > 70:
#         alerts.append({
#             'type': 'rsi_overbought',
#             'symbol': SYMBOL,
#             'interval': INTERVAL,
#             'rsi': round(latest_rsi, 2),
#             'close_price': df['close_price'].iloc[-1]
#         })
#
#     # MACD crossover rules
#     prev_macd = df['macd'].iloc[-2]
#     prev_macd_signal = df['macd_signal'].iloc[-2]
#
#     if prev_macd < prev_macd_signal and latest_macd > latest_macd_signal:
#         alerts.append({
#             'type': 'macd_bullish_crossover',
#             'symbol': SYMBOL,
#             'interval': INTERVAL,
#             'close_price': df['close_price'].iloc[-1]
#         })
#     elif prev_macd > prev_macd_signal and latest_macd < latest_macd_signal:
#         alerts.append({
#             'type': 'macd_bearish_crossover',
#             'symbol': SYMBOL,
#             'interval': INTERVAL,
#             'close_price': df['close_price'].iloc[-1]
#         })
#
#     return alerts
#
# def main():
#     while True:
#         logging.info("Fetching recent klines...")
#         df = get_klines_df(symbol=SYMBOL, interval=INTERVAL, limit=LIMIT)
#         df = add_indicators(df, INDICATORS_CONFIG)
#
#         alerts = check_rules(df)
#         for alert in alerts:
#             producer.produce(alert)
#             logging.info(f"Alert sent: {alert}")
#
#         logging.info("Sleeping for 60 seconds...")
#         time.sleep(60)
#
# if __name__ == '__main__':
#     main()