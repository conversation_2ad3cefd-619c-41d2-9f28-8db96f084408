import datetime
import pandas as pd

from dataclasses import dataclass, field


class SwingMeta(type):
    def __call__(cls, *args, **kwargs):
        # Handle both positional and keyword arguments
        if 'stype' in kwargs:
            stype = kwargs.pop('stype')
        elif len(args) > 2:
            args = list(args)  # Convert args to a list to manipulate it
            stype = args.pop(2)  # Assuming stype is the third argument
        else:
            stype = 'neutral'  # Default value if not provided
        
        # Choose the class based on the stype when Swing is being instantiated
        if cls is Swing:
            if stype == "high":
                return SwingHigh(*args, **kwargs)
            elif stype == "low":
                return SwingLow(*args, **kwargs)
            else:
                # For unrecognized types, create base Swing but preserve the stype
                instance = super().__call__(*args, **kwargs)
                instance.stype = stype  # Explicitly set the provided stype value
                return instance

        # Regular instantiation for the class itself
        instance = super().__call__(*args, **kwargs)

        # Handle direct subclass instantiation to ensure stype is correct
        if cls is SwingHigh:
            instance.stype = 'high'
        elif cls is SwingLow:
            instance.stype = 'low'
        
        return instance


@dataclass
class Swing(metaclass=SwingMeta):
    dt: datetime.datetime
    value: float
    stype: str = field(default="neutral")

    def __post_init__(self):
        if isinstance(self.dt, pd.Timestamp):
            self.dt = self.dt.to_pydatetime()
        elif isinstance(self.dt, str):
            self.dt = datetime.datetime.fromisoformat(self.dt)


@dataclass
class SwingHigh(Swing):
    stype: str = field(init=False, default="high")


@dataclass
class SwingLow(Swing):
    stype: str = field(init=False, default="low")