import pandas as pd
import numpy as np

from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import (
    classification_report, precision_recall_curve,
    f1_score, auc, roc_auc_score, confusion_matrix
)

from src.core.ml.dataset import prepare_training_dataset, finalize_training_dataset
from src.config.base import DEFAULT_TRAINING_CONFIG


def training_pipeline(config: dict, evaluate=True, show_report=False, predict=True):
    df = finalize_training_dataset(prepare_training_dataset(**config))

    # Data split
    train_size = int(len(df) * 0.9)
    X, y = df.drop(columns='target'), df['target']
    X_train, X_test, y_train, y_test = (
        X.iloc[:train_size], X.iloc[train_size:], y.iloc[:train_size], y.iloc[train_size:]
    )

    # Model training
    model = RandomForestClassifier(n_estimators=100, random_state=42).fit(X_train, y_train)
    best_threshold = 0.5  # default value

    evaluation_results = {}
    if evaluate:
        y_pred_proba = model.predict_proba(X_test)[:, 1]
        y_pred = (y_pred_proba >= 0.5).astype(int)
        precision, recall, thresholds = precision_recall_curve(y_test, y_pred_proba)
        roc_auc = roc_auc_score(y_test, y_pred_proba)
        pr_auc = auc(recall, precision)
        cm = confusion_matrix(y_test, y_pred)

        importances = pd.Series(model.feature_importances_, index=X.columns).sort_values(ascending=False)

        if show_report:
            print(classification_report(y_test, y_pred, zero_division=1))
            print(f"\nROC AUC: {roc_auc:.4f}")
            print(f"\nPR AUC: {pr_auc:.4f}")
            print(f"\nConfusion Matrix:\n{cm}")
            print("\nTop 10 Features:")
            print(importances.head(10))

    if predict:
        last_row = X.iloc[[-1]]
        pred_proba = model.predict_proba(last_row)[0][1]
        pred_label = int(pred_proba >= best_threshold)
        confidence = pred_proba * 100
        decision = "BUY" if pred_label else "SELL"

        return {
            "decision": decision,
            "confidence": confidence,
            "threshold": best_threshold,
            "evaluation": evaluation_results if evaluate else None
        }

    return {"evaluation": evaluation_results} if evaluate else None



if __name__ == "__main__":
    config = DEFAULT_TRAINING_CONFIG
    config["ticker"] = "ETHUSDT"
    result = training_pipeline(config=config, evaluate=True, show_report=True, predict=True)
    print(f"\nDecision: {result['decision']}, Confidence: {result['confidence']:.2f}%")
