input {
  # For python-logstash TCP<PERSON>ogstashHand<PERSON> (from your Python code)
  tcp {
    port => 5400  # Match your LOGSTASH_PORT in Python
    codec => json  # python-logstash sends in JSON format
  }
}

filter {
  # Common processing for all logs

  # Add timestamp based on event time if available
  date {
    match => ["@timestamp", "ISO8601"]
    target => "@timestamp"
  }

  # Add source type based on tags
  if "prefect" in [tags] {
    mutate {
      add_field => { "source_type" => "prefect" }
    }
  } else if "crypto-42" in [tags] {
    mutate {
      add_field => { "source_type" => "crypto-42" }
    }
  } else {
    mutate {
      add_field => { "source_type" => "python_application" }
    }
  }

  # Add environment information
  if "dev" in [tags] {
    mutate {
      add_field => { "environment" => "development" }
    }
  }
  else if "prod" in [tags] {
    mutate {
      add_field => { "environment" => "production" }
    }
  }

  # Convert string fields with numeric values to actual numbers
  # This helps with visualization and aggregation in Kibana
  ruby {
    code => "
      event.to_hash.each do |k, v|
        if v.is_a?(String) && v.match(/^-?\d+(\.\d+)?$/)
          if v.include?('.')
            event.set(k, v.to_f)
          else
            event.set(k, v.to_i)
          end
        end
      end
    "
  }
}

output {
  # Send all logs to Elasticsearch with proper indexing
  elasticsearch {
    hosts => ["http://elasticsearch:9200"]

    # Use separate indices based on source
    index => "%{[source_type]}-%{+YYYY.MM.dd}"

    # Fallback index pattern if source_type is not available
    # index => "logs-%{+YYYY.MM.dd}"
  }

  # Debug output to see what's happening (you can remove in production)
  stdout {
    codec => rubydebug
  }
}