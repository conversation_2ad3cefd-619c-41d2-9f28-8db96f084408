import os
import sqlite3
from dataclasses import fields

from src.config.base import DATA_PATH, DB_PATH, DB_SCHEMA_PATH


def create_db_connector(db_name: str) -> sqlite3.Connection:
    if db_name is None:
        raise ValueError("db_name is not provided")
    return sqlite3.connect(DATA_PATH / "sqlite" / f"{db_name}.db")


def apply_schema(db_name: str):
    if not DB_PATH.exists():
        DB_PATH.mkdir(parents=True, exist_ok=True)
    with create_db_connector(db_name) as conn:
        cursor = conn.cursor()
        cursor.executescript(read_sql_from_file(DB_SCHEMA_PATH.as_posix()))
        conn.commit()


def is_db_exists(db_name: str) -> bool:
    return os.path.exists(DATA_PATH / "sqlite" / f"{db_name}.db")


def read_sql_from_file(file_path: str) -> str:
    with open(file_path, 'r') as file:
        query = file.read()
    return query


def escape_column_name(column_name: str) -> str:
    return f'"{column_name}"'


def insert_records(data: list[object], table_name: str, conflict_columns: list[str] = [], db_name: str = None):
    if not data:
        return

    with create_db_connector(db_name) as conn:
        cursor = conn.cursor()

        field_names = [field.name for field in fields(data[0])]
        
        placeholders = ', '.join(['?' for _ in field_names])
        # Escape column names in the field_names_sql
        field_names_sql = ', '.join(escape_column_name(name) for name in field_names)

        # Check if conflict_columns is empty
        if not conflict_columns:
            # Simple INSERT query when there are no conflict columns
            query = f"""
            INSERT INTO {escape_column_name(table_name)} ({field_names_sql})
            VALUES ({placeholders})
            """
        else:
            # Escape column names in the conflict_cols_sql
            conflict_cols_sql = ', '.join(escape_column_name(col) for col in conflict_columns)
            # Escape column names in the update_cols_sql
            update_cols_sql = ', '.join([f"{escape_column_name(col)}=excluded.{escape_column_name(col)}" 
                                         for col in field_names if col not in conflict_columns])

            # INSERT ... ON CONFLICT query when conflict columns are specified
            query = f"""
            INSERT INTO {escape_column_name(table_name)} ({field_names_sql})
            VALUES ({placeholders})
            ON CONFLICT ({conflict_cols_sql}) DO UPDATE SET
            {update_cols_sql}
            """

        prepared_data = [tuple(getattr(instance, field) for field in field_names) for instance in data]
        cursor.executemany(query, prepared_data)

        conn.commit()


def delete_records(table_name: str, where_statement: str = 1, params: tuple = (), db_name: str = None):
    with create_db_connector(db_name) as conn:
        cursor = conn.cursor()
        query = f"DELETE FROM {table_name} WHERE {where_statement}"
        cursor.execute(query, params)
        conn.commit()
        # rows_deleted = cursor.rowcount
        # if rows_deleted == 0:
        #     print("No records found that match the specified criteria.")
        # else:
        #     print(f"{rows_deleted} record(s) have been deleted successfully.")


def fetch_last_n_records(table_name: str, n: int, order_col: str = "id", db_name: str = None):
    with create_db_connector(db_name) as conn:
        cursor = conn.cursor()
        query = f"SELECT * FROM {table_name} ORDER BY {order_col} DESC LIMIT {n}"
        cursor.execute(query)
        records = cursor.fetchall()
        return records


def count_records(table_name: str, db_name: str = None):
    with create_db_connector(db_name) as conn:
        cursor = conn.cursor()
        query = f"SELECT count(*) FROM {table_name}"
        cursor.execute(query)
        records = cursor.fetchall()
        return records[0][0]