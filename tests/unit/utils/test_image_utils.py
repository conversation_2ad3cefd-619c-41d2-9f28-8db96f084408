import os
import base64
import pytest
from PIL import Image

from src.utils.image_utils import concat_images, encode_image


@pytest.fixture
def test_images_dir(request):
    """Create a directory for temporary test images."""
    test_dir = os.path.join("data", "test_images")
    os.makedirs(test_dir, exist_ok=True)
    
    # Add finalizer to remove the directory after tests
    def cleanup():
        if os.path.exists(test_dir):
            # Remove any remaining files in the directory
            for file in os.listdir(test_dir):
                file_path = os.path.join(test_dir, file)
                if os.path.isfile(file_path):
                    os.remove(file_path)
            # Remove the directory itself
            os.rmdir(test_dir)
    
    request.addfinalizer(cleanup)
    return test_dir


@pytest.fixture
def sample_images(test_images_dir):
    """Create sample images for testing."""
    image_paths = []
    
    # Create two test images with different sizes
    img1_path = os.path.join(test_images_dir, "test_img1.png")
    img1 = Image.new('RGB', (100, 150), color='red')
    img1.save(img1_path)
    
    img2_path = os.path.join(test_images_dir, "test_img2.png")
    img2 = Image.new('RGB', (200, 100), color='blue')
    img2.save(img2_path)
    
    image_paths = [img1_path, img2_path]
    
    yield image_paths
    
    # Clean up test images
    for path in image_paths:
        if os.path.exists(path):
            os.remove(path)


class TestConcatImages:
    
    def test_concat_vertical(self, test_images_dir, sample_images):
        output_path = os.path.join(test_images_dir, "vertical_result.png")
        concat_images(sample_images, output_path, orientation='vertical')
        
        # Verify the result
        result_img = Image.open(output_path)
        assert result_img.width == 200  # Max width of the input images
        assert result_img.height == 250  # Sum of heights (150 + 100)
        
        # Clean up
        os.remove(output_path)
    
    def test_concat_horizontal(self, test_images_dir, sample_images):
        output_path = os.path.join(test_images_dir, "horizontal_result.png")
        concat_images(sample_images, output_path, orientation='horizontal')
        
        # Verify the result
        result_img = Image.open(output_path)
        assert result_img.width == 300  # Sum of widths (100 + 200)
        assert result_img.height == 150  # Max height of the input images
        
        # Clean up
        os.remove(output_path)
    
    def test_invalid_orientation(self, test_images_dir, sample_images):
        output_path = os.path.join(test_images_dir, "invalid_result.png")
        
        with pytest.raises(ValueError) as excinfo:
            concat_images(sample_images, output_path, orientation='diagonal')
        
        assert "Orientation must be either 'vertical' or 'horizontal'" in str(excinfo.value)


class TestEncodeImage:
    
    def test_encode_image(self, test_images_dir):
        # Create a simple test image
        test_img_path = os.path.join(test_images_dir, "encode_test.png")
        test_img = Image.new('RGB', (10, 10), color='green')
        test_img.save(test_img_path)
        
        # Test encoding
        encoded = encode_image(test_img_path)
        assert isinstance(encoded, str)
        
        # Verify encoded content can be decoded back
        decoded = base64.b64decode(encoded)
        assert len(decoded) > 0
        
        # Clean up
        os.remove(test_img_path)
