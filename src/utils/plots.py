from pathlib import Path
import plotly.graph_objects as go

from plotly.subplots import make_subplots
from src.core.technical_analysis.data_structures.swings import <PERSON><PERSON><PERSON>, SwingLow, Swing
from src.core.technical_analysis.data_structures.kline import KLine

INDICATOR_COLORS = {
    'sma_14': '#1E90FF',
    'sma_50': '#4682B4',
    'sma_99': '#87CEEB',
    'ema_8': '#FFFF00',
    'ema_20': '#FF69B4',
    'ema_50': '#32CD32',
    'vwap': '#FFFFFF',
    'bb_upper': '#00FFFF',
    'bb_middle': '#FFA500',
    'bb_lower': '#00FFFF',
    'price_channel_high': '#CC9900',
    'price_channel_low': '#663300',
}

INTERVAL_SHIFTS = {"1m": 0.0001, "15m": 0.0005, "1h": 0.001, "4h": 0.005, "1d": 0.01}


def calculate_fibonacci_levels(high, low):
    levels = [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1]
    return [high - (high - low) * l for l in levels]


def calculate_support_resistance(swings, num_levels=3, tolerance=0.015):
    levels, points = [], sorted((s.value for s in swings), reverse=True)
    for p in points:
        if all(abs(level - p) >= tolerance * p for level in levels):
            levels.append(p)
        if len(levels) == num_levels:
            break
    return levels


def add_indicator_traces(fig, klines, indicators):
    for indicator in indicators:
        if hasattr(klines[0], indicator):
            color, width = INDICATOR_COLORS[indicator], 2 if indicator == 'vwap' else 1.5
            name = indicator.replace('_', ' ').title() \
                            .replace('Bb', 'Bollinger')
            fig.add_trace(
                go.Scatter(x=[k.open_time for k in klines], y=[getattr(k, indicator) for k in klines],
                           mode='lines', line=dict(color=color, width=width), name=name),
                row=1, col=1
            )


def plot_candlesticks(klines, swings=None, ticker='', interval='1m',
                      save_path=None, width=None, height=None, show=True, add_indicators=None):

    if add_indicators is None:
        add_indicators = []

    fig = make_subplots(rows=4, cols=1, shared_xaxes=True,
                        vertical_spacing=0.05,
                        row_heights=[0.45, 0.15, 0.2, 0.2],
                        subplot_titles=('Candlesticks', 'Volume', 'MACD', 'RSI'))

    times = [k.open_time for k in klines]

    # Candlestick
    fig.add_trace(go.Candlestick(x=times, open=[k.open_price for k in klines],
                                 high=[k.high_price for k in klines], low=[k.low_price for k in klines],
                                 close=[k.close_price for k in klines], increasing_line_color='green', decreasing_line_color='red'),
                  row=1, col=1)

    add_indicator_traces(fig, klines, add_indicators)

    # Volume
    fig.add_trace(go.Bar(x=times, y=[k.volume for k in klines],
                         marker_color=['green' if k.close_price >= k.open_price else 'red' for k in klines]),
                  row=2, col=1)

    # MACD
    if all(hasattr(klines[0], a) for a in ('macd', 'macd_signal', 'macd_hist')):
        fig.add_trace(go.Scatter(x=times, y=[k.macd for k in klines], line=dict(color='gray'), name='MACD'),
                      row=3, col=1)
        fig.add_trace(go.Scatter(x=times, y=[k.macd_signal for k in klines], line=dict(color='orange'), name='Signal'),
                      row=3, col=1)
        fig.add_trace(go.Bar(x=times, y=[k.macd_hist for k in klines], name='Histogram',
                             marker_color=['green' if h >= 0 else 'red' for h in [k.macd_hist for k in klines]]
                             ),
                      row=3, col=1)

    # RSI
    if hasattr(klines[0], 'rsi'):
        fig.add_trace(go.Scatter(x=times, y=[k.rsi for k in klines], line=dict(color='purple'), name='RSI'),
                      row=4, col=1)
        fig.add_hline(y=70, line_dash='dash', line_color='red', row=4, col=1)
        fig.add_hline(y=30, line_dash='dash', line_color='green', row=4, col=1)

    # Swings
    if swings:
        shift = INTERVAL_SHIFTS[interval]
        for swing in swings:
            if swing.dt in times:
                color, symbol = ('green', 'triangle-up') if isinstance(swing, SwingHigh) else ('red', 'triangle-down')
                offset = swing.value * (1 + shift if color == 'green' else 1 - shift)
                fig.add_trace(go.Scatter(x=[swing.dt], y=[offset], marker=dict(color=color, size=8, symbol=symbol), mode='markers', showlegend=False),
                              row=1, col=1)
        for level in calculate_support_resistance(swings):
            fig.add_hline(y=level, line_dash='dot', line_color='#FF00FF', opacity=0.3,
                          annotation_text=f"{level:.2f}", row=1, col=1)

    fig.update_layout(template='plotly_dark', title=f'{ticker} - {interval} Interval', width=width, height=height)
    fig.update(layout_xaxis_rangeslider_visible=False)
    fig.update_yaxes(range=[0, 100], row=4, col=1)

    if save_path:
        Path(save_path).parent.mkdir(parents=True, exist_ok=True)
        fig.write_image(save_path, width=width, height=height)

    if show:
        fig.show()
