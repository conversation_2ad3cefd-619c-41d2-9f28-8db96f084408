import json
import time

from binance.websocket.um_futures.websocket_client import UMFuturesWebsocketClient

from src.config.base import KAFKA_TOPIC, KAFKA_SERVERS
from src.config.logs import setup_logging
from src.core.realtime.kafka_producer import KafkaProducer
from src.core.technical_analysis.data_structures.kline import KLine
from src.core.technical_analysis.klines_buffer import KLinesBuffer
from src.core.realtime.rules import RuleEngine

logger = setup_logging()


class RealTimeAggregator:
    def __init__(self, symbol, interval_sec=15):
        self.symbol = symbol
        self.interval_sec = interval_sec
        self.trades = []
        self.current_interval = self._current_interval()
        self.buffer = <PERSON><PERSON>inesBuffer(max_size=100)
        self.producer = KafkaProducer(topic=KAFKA_TOPIC, server=KAFKA_SERVERS)
        self.rule_engine = RuleEngine(self.producer, self.buffer)

    def _current_interval(self):
        return int(time.time() // self.interval_sec)

    def add_trade(self, trade):
        interval = self._current_interval()
        if interval != self.current_interval:
            self.finalize_interval()
            self.trades = []
            self.current_interval = interval
        self.trades.append(trade)

    def finalize_interval(self):
        if not self.trades:
            return

        open_price = float(self.trades[0]['p'])
        close_price = float(self.trades[-1]['p'])
        high_price = max(float(t['p']) for t in self.trades)
        low_price = min(float(t['p']) for t in self.trades)
        volume = sum(float(t['q']) for t in self.trades)
        trades_count = len(self.trades)
        buy_volume = sum(float(t['q']) for t in self.trades if not t['m'])
        quote_asset_volume = sum(float(t['p']) * float(t['q']) for t in self.trades)
        taker_buy_quote_asset_volume = sum(
            float(t['p']) * float(t['q']) for t in self.trades if not t['m']
        )

        kline = KLine(
            open_time=self.current_interval * self.interval_sec * 1000,
            open_price=open_price,
            high_price=high_price,
            low_price=low_price,
            close_price=close_price,
            volume=volume,
            close_time=(self.current_interval + 1) * self.interval_sec * 1000 - 1,
            quote_asset_volume=quote_asset_volume,
            number_of_trades=trades_count,
            taker_buy_base_asset_volume=buy_volume,
            taker_buy_quote_asset_volume=taker_buy_quote_asset_volume,
            symbol=self.symbol,
            interval=f"{self.interval_sec}s",
            is_closed=True,
            event_time=int(time.time() * 1000)
        )

        self.buffer.append(kline)
        self.rule_engine.process_kline(kline)
        logger.debug(f"Processed aggregated {self.interval_sec}s K-line: {kline.dict()}")


def handle_trade_message(aggregator):
    def _handler(_, message):
        payload = json.loads(message)
        logger.debug(f"Trade received: {payload}")
        if payload.get('e') == 'aggTrade':
            aggregator.add_trade(payload)
    return _handler


def main(symbol):
    aggregator = RealTimeAggregator(symbol, interval_sec=15)
    ws_client = UMFuturesWebsocketClient(on_message=handle_trade_message(aggregator))
    ws_client.agg_trade(symbol=symbol.lower())

    logger.info(f"Connected to {symbol}@aggTrade WebSocket stream.")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Stopped by user.")
    finally:
        ws_client.stop()
        logger.info("WebSocket closed.")


if __name__ == "__main__":
    main("ETHUSDT")
