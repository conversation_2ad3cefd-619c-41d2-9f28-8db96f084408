import datetime
import logging
from dataclasses import dataclass, field, asdict
from typing import Any, Optional, Union

import pandas as pd

logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

Numeric = Union[float, str, int, None]


@dataclass
class KLine:
    open_time: Any
    open_price: Numeric
    high_price: Numeric
    low_price: Numeric
    close_price: Numeric
    volume: Numeric
    close_time: Any
    quote_asset_volume: Numeric = None
    number_of_trades: Optional[Union[int, str]] = None
    taker_buy_base_asset_volume: Numeric = None
    taker_buy_quote_asset_volume: Numeric = None

    symbol: Optional[str] = field(default=None)
    interval: Optional[str] = field(default=None)
    is_closed: Optional[bool] = field(default=None)
    event_time: Optional[Any] = field(default=None)

    status: Optional[str] = field(default=None, init=False)
    buy_volume_ratio: Optional[float] = field(default=None, init=False)
    sell_volume_ratio: Optional[float] = field(default=None, init=False)

    sma_14: Optional[float] = None
    sma_50: Optional[float] = None
    sma_99: Optional[float] = None
    ema_8: Optional[float] = None
    ema_20: Optional[float] = None
    ema_50: Optional[float] = None
    rsi: Optional[float] = None
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    macd_hist: Optional[float] = None
    bb_middle: Optional[float] = None
    bb_upper: Optional[float] = None
    bb_lower: Optional[float] = None
    price_channel_high: Optional[float] = None
    price_channel_low: Optional[float] = None
    atr: Optional[float] = None
    keltner_middle: Optional[float] = None
    keltner_upper: Optional[float] = None
    keltner_lower: Optional[float] = None
    stoch_k: Optional[float] = None
    stoch_d: Optional[float] = None
    obv: Optional[float] = None
    cci: Optional[float] = None
    williams_r: Optional[float] = None
    ichimoku_tenkan_sen: Optional[float] = None
    ichimoku_kijun_sen: Optional[float] = None
    ichimoku_senkou_span_a: Optional[float] = None
    ichimoku_senkou_span_b: Optional[float] = None
    ichimoku_chikou_span: Optional[float] = None
    vwap: Optional[float] = None

    def __post_init__(self) -> None:
        self.open_time = self._convert_to_datetime(self.open_time)
        self.close_time = self._convert_to_datetime(self.close_time)
        self.event_time = self.event_time if self.event_time is None else self._convert_to_datetime(self.event_time)

        numeric_fields = [
            'open_price', 'high_price', 'low_price', 'close_price', 'volume',
            'quote_asset_volume', 'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume'
        ]
        for field_name in numeric_fields:
            setattr(self, field_name, self._convert_to_float(getattr(self, field_name)))

        if self.number_of_trades is not None:
            self.number_of_trades = self._convert_to_int(self.number_of_trades)

        self._set_status()
        self._calculate_trade_ratio()

    @staticmethod
    def _convert_to_datetime(time_value: Any) -> datetime.datetime:
        if isinstance(time_value, pd.Timestamp):
            return time_value.to_pydatetime()
        if isinstance(time_value, str):
            return datetime.datetime.fromisoformat(time_value)
        if isinstance(time_value, (int, float)):
            return datetime.datetime.fromtimestamp(time_value / 1000 if time_value > 1e12 else time_value)
        if isinstance(time_value, datetime.datetime):
            return time_value
        raise ValueError(f"Unsupported time format: {type(time_value)}")

    @staticmethod
    def _convert_to_float(value: Any) -> float:
        try:
            return float(value)
        except (ValueError, TypeError):
            logger.warning(f"Could not convert {value} to float. Defaulting to 0.")
            return 0.0

    @staticmethod
    def _convert_to_int(value: Any) -> int:
        try:
            return int(float(value))
        except (ValueError, TypeError):
            logger.warning(f"Could not convert {value} to int. Defaulting to 0.")
            return 0

    def _set_status(self) -> None:
        if self.open_price is not None and self.close_price is not None:
            self.status = "green" if self.close_price > self.open_price else "red" if self.close_price < self.open_price else "neutral"
        else:
             self.status = None

    def _calculate_trade_ratio(self) -> None:
        if self.volume and self.volume > 0 and self.taker_buy_base_asset_volume is not None:
            buy_vol = float(self.taker_buy_base_asset_volume)
            self.buy_volume_ratio = buy_vol / self.volume
            self.sell_volume_ratio = 1.0 - self.buy_volume_ratio
        else:
            self.buy_volume_ratio = None
            self.sell_volume_ratio = None

    def dict(self) -> dict[str, Any]:
        # Use init=False fields for calculation before converting to dict
        data = asdict(self)
        # Filter out None values explicitly if desired, or keep them
        data = {k: v for k, v in data.items() if v is not None}

        # Format datetime objects and round floats
        for key, value in data.items():
            if isinstance(value, datetime.datetime):
                 # Using ISO format with UTC timezone identifier
                 data[key] = value.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
            elif isinstance(value, float):
                 # Round floats to a reasonable precision, e.g., 8 decimal places
                 data[key] = round(value, 8)
        return data