import os
import sys
import logging
from logging.config import dictConfig
from typing import Final

from .base import ENV, APP_NAME

LOGSTASH_HOST: Final[str] = os.getenv('LOGSTASH_HOST', '127.0.0.1')
LOGSTASH_PORT: Final[int] = int(os.getenv('LOGSTASH_PORT', 5400))
USE_LOGSTASH: Final[bool] = os.getenv('USE_LOGSTASH', 'False').lower() == 'true'

DEBUG: Final[bool] = (ENV == "dev")
LOG_LEVEL: Final[int] = logging.DEBUG if DEBUG else logging.INFO
BINANCE_LOG_LEVEL: Final[int] = logging.DEBUG if DEBUG else logging.INFO

# Add a flag to identify if we're in a Prefect flow
IN_PREFECT_FLOW: Final[bool] = os.getenv('IN_PREFECT_FLOW', 'False').lower() == 'true'


def setup_logging(logger_name=None):
    """
    Configure logging with optional Logstash integration.

    Args:
        logger_name: Optional name for the logger, useful for filtering in Kibana
                    If None, uses the root logger
    """
    handlers = {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'DEBUG' if DEBUG else 'INFO',
            'formatter': 'standard',
            'stream': 'ext://sys.stdout',
        },
    }

    if USE_LOGSTASH:
        try:
            import logstash

            # Set appropriate tags based on context
            tags = [APP_NAME, ENV]
            if IN_PREFECT_FLOW:
                tags.append('prefect')  # Add prefect tag if in a flow

            handlers['logstash'] = {
                'class': 'logstash.TCPLogstashHandler',
                'level': 'INFO',  # Logstash usually gets INFO level and above
                'host': LOGSTASH_HOST,
                'port': LOGSTASH_PORT,
                'version': 1,  # Required by the library
                'tags': tags,  # Add tags for context and filtering
                'message_type': 'logstash',  # Optional: specify message type
                'fqdn': False,  # Don't include FQDN to reduce log size
            }
        except ImportError:
            logging.warning("Logstash handler configured but 'python-logstash' library not found. Skipping.")

    logging_config = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'standard': {
                'format': '[%(asctime)s] - %(levelname)s - %(name)s - %(module)s:%(lineno)d - %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S',
            },
        },
        'handlers': handlers,
        'root': {
            'level': LOG_LEVEL,
            'handlers': list(handlers.keys()),
        },
    }

    # Add specific logger configuration if a logger_name is provided
    if logger_name:
        logging_config['loggers'] = {
            logger_name: {
                'level': LOG_LEVEL,
                'handlers': list(handlers.keys()),
                'propagate': False,  # Don't propagate to root logger to avoid duplicate logs
            }
        }

    dictConfig(logging_config)

    # If a specific logger was requested, return that. Otherwise, return root logger
    if logger_name:
        return logging.getLogger(logger_name)

    # Setup binance log level
    from binance.lib.utils import config_logging
    config_logging(logging, BINANCE_LOG_LEVEL)

    # Set up exception hook for uncaught exceptions
    def handle_exception(exc_type, exc_value, exc_traceback):
        # Prevent logging KeyboardInterrupt as an error
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        # Get logger for the context where exception occurred if possible, else root
        logger = logging.getLogger(__name__)  # Or potentially inspect traceback for source module
        logger.critical("Uncaught exception", exc_info=(exc_type, exc_value, exc_traceback))

    sys.excepthook = handle_exception

    return logging.getLogger()


# Add a function specifically for Prefect flows
def setup_prefect_logging():
    """
    Configure logging for Prefect flows to properly send logs to Logstash
    """
    # Set environment variable to indicate we're in a Prefect flow
    os.environ['IN_PREFECT_FLOW'] = 'True'

    # Configure the base logging
    logger = setup_logging(logger_name='prefect')

    # Try to get the Prefect run logger, but fall back to the standard logger if not in a flow context
    try:
        from prefect.logging import get_run_logger
        from prefect.context import FlowRunContext, TaskRunContext

        # Only get the run logger if we're in a flow or task context
        try:
            # This will throw MissingContextError if not in a flow/task
            prefect_logger = get_run_logger()

            # Add additional context to all log messages if desired
            class PrefectLogAdapter(logging.LoggerAdapter):
                def process(self, msg, kwargs):
                    # Add any extra context needed for Prefect logs
                    if 'extra' not in kwargs:
                        kwargs['extra'] = {}

                    # Add flow run info if available
                    try:
                        from prefect.context import get_run_context
                        context = get_run_context()
                        if context and hasattr(context, 'flow_run'):
                            kwargs['extra']['flow_run_id'] = getattr(context.flow_run, 'id', None)
                            kwargs['extra']['flow_run_name'] = getattr(context.flow_run, 'name', None)
                        if context and hasattr(context, 'task_run'):
                            kwargs['extra']['task_run_id'] = getattr(context.task_run, 'id', None)
                            kwargs['extra']['task_run_name'] = getattr(context.task_run, 'name', None)
                    except Exception as e:
                        # If we can't get context, just continue
                        pass

                    return msg, kwargs

            return PrefectLogAdapter(prefect_logger, {})

        except Exception as e:
            # If we're not in a flow context, just use the standard logger
            return logger

    except ImportError:
        logging.warning("Prefect not found. Using standard logger instead.")
        return logger