import datetime
import logging

from typing import Union, cast

from binance.spot import Spot
from binance.cm_futures import CMFutures
from binance.um_futures import UMFutures

from src.config.base import BINANCE_API_KEY, BINANCE_SECRET_KEY
from src.config.logs import setup_logging

setup_logging()
logger = setup_logging("binance_client")

BinanceClient = Union[Spot, CMFutures, UMFutures]


def get_client(source: str) -> BinanceClient:
    """
    Factory function to get a Binance API client instance.

    Args:
        source: The client type ('spot', 'cm_futures', 'um_futures').

    Returns:
        An initialized Binance API client instance.

    Raises:
        ValueError: If source is invalid or API keys are not configured.
    """
    if not BINANCE_API_KEY or not BINANCE_SECRET_KEY:
        raise ValueError("API Key/Secret not configured.")

    if source == "spot":
        return Spot(api_key=BINANCE_API_KEY, api_secret=BINANCE_SECRET_KEY)
    elif source == "cm_futures":
        return CMFutures(key=BINANCE_API_KEY, secret=BINANCE_SECRET_KEY)
    elif source == "um_futures":
        return UMFutures(key=BINANCE_API_KEY, secret=BINANCE_SECRET_KEY)
    else:
        logger.error(f"Invalid client source requested: {source}")
        raise ValueError("Invalid source. Must be either 'spot', 'cm_futures', or 'um_futures'.")


def get_server_time(to_str: bool = True, client_name: str = "um_futures") -> Union[datetime.datetime, str]:
    """
    Fetches the server time from Binance.

    Args:
        to_str: If True, return time as formatted string. Otherwise, datetime object.
        client_name: The type of client to use ('spot', 'cm_futures', 'um_futures').

    Returns:
        The server time as a datetime object or formatted string.

    Raises:
        Exceptions from the underlying binance client library (e.g., network errors).
        ValueError: If client_name is invalid or API keys are missing.
    """
    try:
        client = get_client(client_name)
        server_time_ms = client.time()['serverTime']
        server_dt = datetime.datetime.fromtimestamp(server_time_ms / 1000.0)

        if to_str:
            return server_dt.strftime('%Y-%m-%d %H:%M:%S')
        else:
            return cast(datetime.datetime, server_dt)

    except KeyError as e:
        logger.error(f"API keys likely missing or invalid: {e}")
        raise ValueError("Failed to initialize client, check API keys.") from e
    except Exception as e:
        logger.error(f"Failed to get server time using {client_name} client: {e}", exc_info=True)
        raise


if __name__ == '__main__':
    print(get_server_time())