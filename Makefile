# --- Configuration ---
# Define the shared network name (must match 'docker network create <name>')
SHARED_NETWORK := app-shared-network

# Define base directory for deployments
DEPLOY_DIR := deploy

# Define directories for each stack
PREFECT_DIR := $(DEPLOY_DIR)/prefect
KAFKA_DIR := $(DEPLOY_DIR)/kafka
ELK_DIR := $(DEPLOY_DIR)/elk
APP_DIR := $(DEPLOY_DIR)/app # Assuming an 'app' stack directory

# Define compose file paths
PREFECT_COMPOSE := $(PREFECT_DIR)/docker-compose.yml
KAFKA_COMPOSE := $(KAFKA_DIR)/docker-compose.yml
ELK_COMPOSE := $(ELK_DIR)/docker-compose.yml
APP_COMPOSE := $(APP_DIR)/docker-compose.yml # Adjust if your app compose file differs

# Define optional .env file paths for each stack
PREFECT_ENV := $(PREFECT_DIR)/.env
KAFKA_ENV := $(KAFKA_DIR)/.env
ELK_ENV := $(ELK_DIR)/.env
APP_ENV := $(APP_DIR)/.env # Adjust if your app env file differs

# Helper to pass arguments AFTER the target name (e.g., for logs, build)
ARGS = $(filter-out $@,$(MAKECMDGOALS))

# Stacks list for iteration (add/remove as needed)
STACKS := prefect kafka elk app

# --- Targets ---

.PHONY: help network-create network-rm $(STACKS) \
	up-% down-% down-v-% restart-% logs-% ps-% build-% \
	up-all down-all down-v-all restart-all ps-all build-all exec clean prune

help: ## Show this help message
	@echo "Usage: make [target] [arguments...]"
	@echo ""
	@echo "Network Management:"
	@echo "  make network-create     Create the shared Docker network ($(SHARED_NETWORK))"
	@echo "  make network-rm         Remove the shared Docker network"
	@echo ""
	@echo "Aggregate Stack Management:"
	@echo "  make up-all             Start all stacks ($(STACKS))"
	@echo "  make down-all           Stop all stacks"
	@echo "  make down-v-all         Stop all stacks and remove volumes"
	@echo "  make restart-all        Restart all stacks"
	@echo "  make ps-all             Show status for all stacks"
	@echo "  make build-all          Build images for all stacks"
	@echo ""
	@echo "Individual Stack Management (replace '%' with stack name: $(STACKS)):"
	@echo "  make up-%               Start the specified stack"
	@echo "  make down-%             Stop the specified stack"
	@echo "  make down-v-%           Stop the specified stack and remove volumes"
	@echo "  make restart-%          Restart the specified stack"
	@echo "  make logs-% [service]   Tail logs for the stack (optionally specify service)"
	@echo "  make ps-%               Show status for the specified stack"
	@echo "  make build-% [service]  Build images for the stack (optionally specify service)"
	@echo ""
	@echo "Other Commands:"
	@echo "  make exec <stack> <service> <command>  Execute a command in a service container"
	@echo "  make clean              Alias for down-v-all"
	@echo "  make prune              Remove stopped containers and unused Docker resources"
	@echo ""
	@echo "Examples:"
	@echo "  make network-create"
	@echo "  make up-kafka up-app"
	@echo "  make logs-prefect prefect-agent"
	@echo "  make build-app"
	@echo "  make exec prefect prefect-server bash"
	@echo "  make down-all"


# -- Network Management --
network-create: ## Create the shared Docker network if it doesn't exist
	@docker network inspect $(SHARED_NETWORK) > /dev/null 2>&1 || \
		(echo "INFO: Creating shared network '$(SHARED_NETWORK)'..." && docker network create $(SHARED_NETWORK))

network-rm: ## Remove the shared Docker network (ensure stacks are down first)
	@echo "INFO: Removing shared network '$(SHARED_NETWORK)'..."
	@docker network rm $(SHARED_NETWORK) || echo "WARN: Network '$(SHARED_NETWORK)' not found or already removed."


# -- Aggregate Stack Targets --
# Note: Startup order here is arbitrary unless specific dependencies exist between stacks
# which must be handled manually or by adjusting target dependencies.
up-all: network-create $(addprefix up-, $(STACKS)) ## Start all stacks
	@echo "INFO: All stacks started."

down-all: $(addprefix down-, $(STACKS)) ## Stop all stacks
	@echo "INFO: All stacks stopped."

down-v-all: $(addprefix down-v-, $(STACKS)) ## Stop all stacks and remove volumes
	@echo "INFO: All stacks stopped and volumes removed."

restart-all: down-all up-all ## Restart all stacks

ps-all: ## Show status for all stacks
	@$(foreach stack,$(STACKS), \
		echo ""; echo "--- Status for stack: $(stack) ---"; $(MAKE) ps-$(stack); \
	)

build-all: ## Build images for all stacks
	@$(foreach stack,$(STACKS), \
		echo ""; echo "--- Building stack: $(stack) ---"; $(MAKE) build-$(stack); \
	)


# -- Individual Stack Targets (using pattern rules) --

# Generic 'up' target for any stack
up-%: network-create ## Start the specified stack (e.g., make up-prefect)
	@$(eval STACK_NAME := $*)
	@$(eval _COMPOSE_FILE := $(DEPLOY_DIR)/$(STACK_NAME)/docker-compose.yml)
	@$(eval _ENV_FILE := $(DEPLOY_DIR)/$(STACK_NAME)/.env)
	@echo "INFO: Starting stack [$(STACK_NAME)]..."
	@docker compose -f $(_COMPOSE_FILE) --env-file $(_ENV_FILE) up -d

# Generic 'down' target
down-%: ## Stop the specified stack (e.g., make down-prefect)
	@$(eval STACK_NAME := $*)
	@$(eval _COMPOSE_FILE := $(DEPLOY_DIR)/$(STACK_NAME)/docker-compose.yml)
	@$(eval _ENV_FILE := $(DEPLOY_DIR)/$(STACK_NAME)/.env)
	@echo "INFO: Stopping stack [$(STACK_NAME)]..."
	@docker compose -f $(_COMPOSE_FILE) --env-file $(_ENV_FILE) down

# Generic 'down-v' target (remove volumes)
down-v-%: ## Stop the specified stack and remove volumes (e.g., make down-v-prefect)
	@$(eval STACK_NAME := $*)
	@$(eval _COMPOSE_FILE := $(DEPLOY_DIR)/$(STACK_NAME)/docker-compose.yml)
	@$(eval _ENV_FILE := $(DEPLOY_DIR)/$(STACK_NAME)/.env)
	@echo "INFO: Stopping stack [$(STACK_NAME)] and removing volumes..."
	@docker compose -f $(_COMPOSE_FILE) --env-file $(_ENV_FILE) down -v

# Generic 'restart' target
restart-%: down-% up-% ## Restart the specified stack (e.g., make restart-prefect)

# Generic 'logs' target
logs-%: ## Tail logs for the specified stack (e.g., make logs-prefect [service])
	@$(eval STACK_NAME := $*)
	@$(eval _COMPOSE_FILE := $(DEPLOY_DIR)/$(STACK_NAME)/docker-compose.yml)
	@$(eval _ENV_FILE := $(DEPLOY_DIR)/$(STACK_NAME)/.env)
	@echo "INFO: Tailing logs for stack [$(STACK_NAME)], services: $(if $(ARGS),$(ARGS),'all')"
	@docker compose -f $(_COMPOSE_FILE) --env-file $(_ENV_FILE) logs -f $(ARGS)

# Generic 'ps' target
ps-%: ## Show status for the specified stack (e.g., make ps-prefect)
	@$(eval STACK_NAME := $*)
	@$(eval _COMPOSE_FILE := $(DEPLOY_DIR)/$(STACK_NAME)/docker-compose.yml)
	@$(eval _ENV_FILE := $(DEPLOY_DIR)/$(STACK_NAME)/.env)
	@docker compose -f $(_COMPOSE_FILE) --env-file $(_ENV_FILE) ps

# Generic 'build' target
build-%: ## Build images for the specified stack (e.g., make build-app [service])
	@$(eval STACK_NAME := $*)
	@$(eval _COMPOSE_FILE := $(DEPLOY_DIR)/$(STACK_NAME)/docker-compose.yml)
	@$(eval _ENV_FILE := $(DEPLOY_DIR)/$(STACK_NAME)/.env)
	@echo "INFO: Building images for stack [$(STACK_NAME)], services: $(if $(ARGS),$(ARGS),'all')"
	@docker compose -f $(_COMPOSE_FILE) --env-file $(_ENV_FILE) build $(ARGS)


# -- Other Commands --

exec: ## Execute command in service container (e.g., make exec prefect prefect-server bash)
	@$(eval STACK_NAME := $(firstword $(ARGS)))
	@$(eval EXEC_ARGS := $(wordlist 2, $(words $(ARGS)), $(ARGS)))
	@$(if $(STACK_NAME),, $(error Please specify stack name (prefect, kafka, elk, app)))
	@$(if $(EXEC_ARGS),, $(error Please specify service name and command))
	@$(eval _COMPOSE_FILE := $(DEPLOY_DIR)/$(STACK_NAME)/docker-compose.yml)
	@$(eval _ENV_FILE := $(DEPLOY_DIR)/$(STACK_NAME)/.env)
	@$(if $(filter $(STACK_NAME), $(STACKS)),, $(error Invalid stack name '$(STACK_NAME)'. Choose from: $(STACKS)))
	@echo "INFO: Executing in stack [$(STACK_NAME)] -> [$(EXEC_ARGS)]..."
	@docker compose -f $(_COMPOSE_FILE) --env-file $(_ENV_FILE) exec $(EXEC_ARGS)

clean: down-v-all ## Alias for down-v-all (stops services and removes volumes)

prune: ## Remove stopped containers and unused Docker resources (use with caution)
	@echo "WARNING: Pruning Docker system (stopped containers, unused networks/images/volumes)..."
	@docker system prune -af --volumes


# Prevent intermediate file deletion
.SECONDARY:

# Default target
.DEFAULT_GOAL := help