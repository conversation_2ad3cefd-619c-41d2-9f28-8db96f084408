# import logging
# import time
# import json
# import pandas as pd
# from confluent_kafka import Producer
#
# from src.core.ml.pipeline import training_pipeline
# from src.core.realtime.kafka_producer import KafkaProducer
#
# from src.config.base import KAFKA_SERVERS, KAFKA_TOPIC
# from src.config.logs import setup_logging
#
#
# SYMBOL = 'SOLUSDT'
# INTERVAL = '15m'
#
#
# def main():
#     logger = setup_logging()
#
#     producer = Producer({
#         'bootstrap.servers': KAFKA_SERVERS,
#         'group.id': 'telegram_bot_group',
#         'auto.offset.reset': 'latest'
#     })
#
#     logger.info("Starting ML prediction pipeline...")
#
#     prediction, confidence = training_pipeline(show_report=False)
#
#     alert = {
#         'type': 'ml_prediction',
#         'symbol': SYMBOL,
#         'interval': INTERVAL,
#         'prediction': "🟢 GREEN" if prediction == 1 else "🔴 RED",
#         'confidence': round(confidence, 2),
#         'timestamp': pd.Timestamp.now().isoformat()
#     }
#
#     producer.produce(KAFKA_TOPIC, json.dumps(alert).encode('utf-8'))
#     producer.flush()
#
#     logger.info(f"ML prediction sent: {alert}")
#
#
# if __name__ == '__main__':
#     main()
