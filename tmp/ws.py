import logging
import time
import json
from binance.websocket.um_futures.websocket_client import UMFuturesWebsocketClient
from binance.lib.utils import config_logging

from src.core.technical_analysis.data_structures.kline import KLine


# Optional: Configure logging to see connection details and messages
config_logging(logging, logging.INFO)

def handle_kline_message(_, message):
    """Processes incoming WebSocket K-line messages and creates KLine objects."""
    try:
        payload = json.loads(message)

        if 'e' in payload and payload['e'] == 'kline':
            kline_data = payload['k']

            # Create KLine object using data from the payload
            kline_object = KLine(
                open_time=kline_data['t'],
                open_price=kline_data['o'],
                high_price=kline_data['h'],
                low_price=kline_data['l'],
                close_price=kline_data['c'],
                volume=kline_data['v'],
                close_time=kline_data['T'],
                quote_asset_volume=kline_data['q'],
                number_of_trades=kline_data['n'],
                taker_buy_base_asset_volume=kline_data['V'],
                taker_buy_quote_asset_volume=kline_data['Q'],
                symbol=kline_data['s'],     # Store symbol
                interval=kline_data['i'],   # Store interval
                is_closed=kline_data['x'],   # Store closed status
                event_time=payload['E']
            )

            # Now you can easily work with the kline_object
            # Example: Print the created object (uses the dataclass's __repr__)
            print(f"Received KLine object: {kline_object.dict()}")

            # Example: Access specific attributes
            if kline_object.is_closed:
                print(f"  --> KLINE CLOSED: Symbol={kline_object.symbol}, Close={kline_object.close_price}, Status={kline_object.status}")

            # Example: Get dictionary representation
            # print(f"  --> KLine as dict: {kline_object.dict()}")

            # --- Add your logic here using the kline_object ---
            # e.g., append kline_object to a list, database, perform calculations, etc.

        elif 'result' in payload and payload['result'] is None:
            logging.info(f"Subscription confirmation received: {payload}")
        else:
            logging.warning(f"Received non-kline message: {payload}")

    except json.JSONDecodeError:
        logging.error(f"Failed to decode JSON: {message}")
    except KeyError as e:
        logging.error(f"Missing key in kline data: {e} - Payload: {payload}")
    except Exception as e:
        logging.error(f"Error processing message: {e.__class__.__name__}: {e}")
        logging.error(f"Raw message: {message}")


# --- Configuration ---
symbol_to_track = 'SOLUSDT' # The futures symbol you want to track
interval_to_track = '1m'    # The K-line interval ('1m', '5m', '15m', '1h', '4h', '1d', etc.)

# --- Initialize and Start WebSocket Client ---
my_client = UMFuturesWebsocketClient(on_message=handle_kline_message)

logging.info(f"Subscribing to {symbol_to_track.lower()}@{interval_to_track} kline stream...")
my_client.kline(
    symbol=symbol_to_track.lower(),
    interval=interval_to_track
)

logging.info("WebSocket connection established. Listening for messages... Press Ctrl+C to stop.")
try:
    while True:
        time.sleep(60) # Keep the main thread alive
except KeyboardInterrupt:
    logging.info("Interrupted by user. Closing WebSocket connection...")
finally:
    my_client.stop()
    logging.info("WebSocket connection closed.")