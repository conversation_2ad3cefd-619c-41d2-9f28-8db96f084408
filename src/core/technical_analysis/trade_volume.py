import pandas as pd
import numpy as np


def detect_trading_spikes(
    df: pd.DataFrame,
    moving_average_period: int = 3,
    method: str = 'sma',
    volume_threshold_multiplier: float = 2,
    trades_threshold_multiplier: float = 2,
    n_std: int = 2,
    buy_sell_ratio_threshold: float = 0.501
) -> pd.DataFrame:
    df = df.copy()
    df = df.sort_values(by='open_time').reset_index(drop=True)

    # Initialize the 'trade_spike' column with zeros
    df['trade_spike'] = pd.Series(0, index=df.index, dtype='int8')

    # Helper function to calculate thresholds
    def get_threshold(series, method, period, multiplier, n_std):
        if method == 'sma':
            avg = series.rolling(window=period, min_periods=1).mean()
            threshold = avg * multiplier
        elif method == 'ema':
            avg = series.ewm(span=period, adjust=False).mean()
            threshold = avg * multiplier
        elif method == 'std':
            avg = series.rolling(window=period, min_periods=1).mean()
            std = series.rolling(window=period, min_periods=1).std()
            threshold = avg + n_std * std
        else:
            raise ValueError(f"Unknown method: {method}")
        return threshold

    # Calculate thresholds for volume and number of trades
    volume_threshold = get_threshold(
        df['volume'], method, moving_average_period, volume_threshold_multiplier, n_std
    )
    trades_threshold = get_threshold(
        df['number_of_trades'], method, moving_average_period, trades_threshold_multiplier, n_std
    )

    # Identify sudden spikes
    df['sudden_volume_spike'] = df['volume'] >= volume_threshold
    df['sudden_trades_spike'] = df['number_of_trades'] >= trades_threshold

    # Calculate buy/sell volume ratios
    df['buy_volume_ratio'] = df['taker_buy_base_asset_volume'] / df['volume'].replace(0, np.nan)
    df['buy_volume_ratio'].fillna(0, inplace=True)
    df['sell_volume_ratio'] = 1 - df['buy_volume_ratio']

    # Determine spike type
    spike_condition = df['sudden_volume_spike'] | df['sudden_trades_spike']
    buy_condition = df['buy_volume_ratio'] >= buy_sell_ratio_threshold
    sell_condition = df['buy_volume_ratio'] <= (1 - buy_sell_ratio_threshold)

    df.loc[spike_condition & buy_condition, 'trade_spike'] = 1  # Buying spike
    df.loc[spike_condition & sell_condition, 'trade_spike'] = 2  # Selling spike
    df.loc[spike_condition & ~(buy_condition | sell_condition), 'trade_spike'] = 3  # Neutral

    # Clean up temporary columns
    df.drop(
        labels=['sudden_volume_spike', 'sudden_trades_spike', 'buy_volume_ratio', 'sell_volume_ratio'],
        axis=1,
        inplace=True
    )

    return df