import inspect
import numpy as np
import pandas as pd


def calculate_sma(df: pd.DataFrame, window: int = 20, label: str = 'close_price', inplace: bool = False):
    """
    Calculate Simple Moving Average (SMA) for a given DataFrame.

    Adds a column named 'sma_{window}'.

    Args:
        df (pd.DataFrame): Input DataFrame with time series data.
        window (int): The rolling window size for the SMA. Defaults to 20.
        label (str): The name of the column containing the price data to use. Defaults to 'close_price'.
        inplace (bool): If True, modify the DataFrame in place. Otherwise, return a modified copy. Defaults to False.

    Returns:
        pd.DataFrame or None: The DataFrame with the SMA column added, or None if inplace=True.

    Raises:
        ValueError: If the DataFrame length is less than the specified window.
        KeyError: If the specified `label` column does not exist in the DataFrame.
    """
    if label not in df.columns:
        raise KeyError(f"Column '{label}' not found in DataFrame.")
    if len(df) < window:
        raise ValueError(f"DataFrame length ({len(df)}) is less than the specified window ({window}) for SMA calculation.")

    if not inplace:
        df = df.copy()

    sma_col_name = f'sma_{window}'
    df[sma_col_name] = df[label].rolling(window=window).mean()

    if inplace:
        return None
    else:
        return df


def calculate_ema(df: pd.DataFrame, span: int = 20, label: str = 'close_price', inplace: bool = False):
    """
    Calculate Exponential Moving Average (EMA) for a given DataFrame.

    Uses span for calculation (common convention, alpha=2/(span+1)).
    Adds a column named 'ema_{span}'.

    Args:
        df (pd.DataFrame): Input DataFrame with time series data.
        span (int): The span for the EMA calculation. Defaults to 20.
        label (str): The name of the column containing the price data to use. Defaults to 'close_price'.
        inplace (bool): If True, modify the DataFrame in place. Otherwise, return a modified copy. Defaults to False.

    Returns:
        pd.DataFrame or None: The DataFrame with the EMA column added, or None if inplace=True.

    Raises:
        ValueError: If the DataFrame length is less than the specified span (though EMA technically calculates from the start).
        KeyError: If the specified `label` column does not exist in the DataFrame.
    """
    if label not in df.columns:
        raise KeyError(f"Column '{label}' not found in DataFrame.")
    # Note: EMA can technically start calculating earlier than the span,
    # but this check maintains consistency and avoids calculations on overly short series.
    if len(df) < span:
        raise ValueError(f"DataFrame length ({len(df)}) is less than the specified span ({span}) for EMA calculation.")

    if not inplace:
        df = df.copy()

    ema_col_name = f'ema_{span}'
    # adjust=False matches behavior often seen in trading platforms
    df[ema_col_name] = df[label].ewm(span=span, adjust=False).mean()

    if inplace:
        return None
    else:
        return df


def calculate_rsi(df: pd.DataFrame, window: int = 14, label: str = 'close_price', inplace: bool = False):
    """
    Calculate Relative Strength Index (RSI) for a given DataFrame.

    Uses Wilder's smoothing method (equivalent to EMA with alpha=1/window).
    Adds a column named 'rsi'.

    Args:
        df (pd.DataFrame): Input DataFrame with time series data.
        window (int): The period for RSI calculation. Defaults to 14.
        label (str): The name of the column containing the price data to use. Defaults to 'close_price'.
        inplace (bool): If True, modify the DataFrame in place. Otherwise, return a modified copy. Defaults to False.

    Returns:
        pd.DataFrame or None: The DataFrame with the RSI column added, or None if inplace=True.

    Raises:
        ValueError: If the DataFrame length is less than the specified window + 1 (needed for diff).
        KeyError: If the specified `label` column does not exist in the DataFrame.
    """
    if label not in df.columns:
        raise KeyError(f"Column '{label}' not found in DataFrame.")
    # Need at least window + 1 rows to calculate the first diff and then the initial average
    if len(df) < window + 1:
         raise ValueError(f"DataFrame length ({len(df)}) is less than the required ({window + 1}) for RSI window {window}.")

    if not inplace:
        df = df.copy()

    delta = df[label].diff()
    gain = delta.where(delta > 0, 0.0) # Use 0.0 for float consistency
    loss = -delta.where(delta < 0, 0.0) # Use 0.0 for float consistency

    # Use Wilder's smoothing method (equivalent to com=window-1)
    # min_periods=window ensures we have enough data before starting calculation
    avg_gain = gain.ewm(alpha=1/window, min_periods=window, adjust=False).mean()
    avg_loss = loss.ewm(alpha=1/window, min_periods=window, adjust=False).mean()

    # Calculate RS
    # Handle division by zero: if avg_loss is 0, RS is infinite (or NaN if avg_gain is also 0)
    rs = avg_gain / avg_loss
    # Replace inf values with a large number or handle appropriately if needed,
    # but pandas handles inf in the next step reasonably (RSI -> 100).
    # For total clarity, explicitly handle:
    # rs = rs.replace([float('inf')], 1e6) # Optional: Replace inf with a large number

    # Calculate RSI
    rsi_col_name = 'rsi'
    df[rsi_col_name] = 100.0 - (100.0 / (1.0 + rs))
    # Handle cases where avg_loss was 0 (rs=inf -> rsi=100) or both avg_gain/avg_loss were 0 (rs=NaN -> rsi=NaN)

    if inplace:
        return None
    else:
        return df


def calculate_macd(df: pd.DataFrame, label: str = 'close_price', fast_period: int = 12, slow_period: int = 26, signal_period: int = 9, inplace: bool = False):
    """
    Calculate Moving Average Convergence Divergence (MACD) indicators.

    Adds columns: 'macd', 'macd_signal', 'macd_hist'.

    Args:
        df (pd.DataFrame): Input DataFrame with time series data.
        label (str): The name of the column containing the price data to use. Defaults to 'close_price'.
        fast_period (int): Span for the fast EMA. Defaults to 12.
        slow_period (int): Span for the slow EMA. Defaults to 26.
        signal_period (int): Span for the signal line EMA. Defaults to 9.
        inplace (bool): If True, modify the DataFrame in place. Otherwise, return a modified copy. Defaults to False.

    Returns:
        pd.DataFrame or None: The DataFrame with MACD columns added, or None if inplace=True.

    Raises:
        ValueError: If the DataFrame length is less than the slow_period.
        KeyError: If the specified `label` column does not exist in the DataFrame.
    """
    if label not in df.columns:
        raise KeyError(f"Column '{label}' not found in DataFrame.")
    if slow_period <= fast_period:
         raise ValueError("slow_period must be greater than fast_period for MACD calculation.")
    # Need at least slow_period data points for the slowest EMA to be meaningful
    if len(df) < slow_period:
        raise ValueError(f"DataFrame length ({len(df)}) is less than the required slow_period ({slow_period}) for MACD calculation.")

    if not inplace:
        df = df.copy()

    fast_ema = df[label].ewm(span=fast_period, adjust=False).mean()
    slow_ema = df[label].ewm(span=slow_period, adjust=False).mean()

    macd_col_name = 'macd'
    signal_col_name = 'macd_signal'
    hist_col_name = 'macd_hist'

    df[macd_col_name] = fast_ema - slow_ema
    df[signal_col_name] = df[macd_col_name].ewm(span=signal_period, adjust=False).mean()
    df[hist_col_name] = df[macd_col_name] - df[signal_col_name]

    if inplace:
        return None
    else:
        return df


def calculate_bollinger_bands(df: pd.DataFrame, window: int = 20, num_std: float = 2.0, label: str = 'close_price', inplace: bool = False):
    """
    Calculate Bollinger Bands for a given DataFrame.

    Adds columns: 'bb_middle', 'bb_upper', 'bb_lower'.

    Args:
        df (pd.DataFrame): Input DataFrame with time series data.
        window (int): The rolling window size for the middle band (SMA) and standard deviation. Defaults to 20.
        num_std (float): The number of standard deviations for the upper and lower bands. Defaults to 2.0.
        label (str): The name of the column containing the price data to use. Defaults to 'close_price'.
        inplace (bool): If True, modify the DataFrame in place. Otherwise, return a modified copy. Defaults to False.

    Returns:
        pd.DataFrame or None: The DataFrame with Bollinger Band columns added, or None if inplace=True.

    Raises:
        ValueError: If the DataFrame length is less than the specified window.
        KeyError: If the specified `label` column does not exist in the DataFrame.
    """
    if label not in df.columns:
        raise KeyError(f"Column '{label}' not found in DataFrame.")
    if len(df) < window:
        raise ValueError(f"DataFrame length ({len(df)}) is less than the specified window ({window}) for Bollinger Bands calculation.")

    if not inplace:
        df = df.copy()

    middle_col_name = 'bb_middle'
    upper_col_name = 'bb_upper'
    lower_col_name = 'bb_lower'

    df[middle_col_name] = df[label].rolling(window=window).mean()
    rolling_std = df[label].rolling(window=window).std()
    df[upper_col_name] = df[middle_col_name] + (rolling_std * num_std)
    df[lower_col_name] = df[middle_col_name] - (rolling_std * num_std)

    if inplace:
        return None
    else:
        return df


def calculate_price_channel(df: pd.DataFrame, window: int = 20, high_label: str = 'high_price', low_label: str = 'low_price', inplace: bool = False):
    """
    Calculate Price Channel (Donchian Channel variant) for a given DataFrame.

    Adds columns: 'price_channel_high', 'price_channel_low'.

    Args:
        df (pd.DataFrame): Input DataFrame with time series data (requires high and low prices).
        window (int): The rolling window size for finding max high and min low. Defaults to 20.
        high_label (str): The name of the column containing high prices. Defaults to 'high_price'.
        low_label (str): The name of the column containing low prices. Defaults to 'low_price'.
        inplace (bool): If True, modify the DataFrame in place. Otherwise, return a modified copy. Defaults to False.

    Returns:
        pd.DataFrame or None: The DataFrame with Price Channel columns added, or None if inplace=True.

    Raises:
        ValueError: If the DataFrame length is less than the specified window.
        KeyError: If the specified `high_label` or `low_label` columns do not exist in the DataFrame.
    """
    if high_label not in df.columns:
        raise KeyError(f"Column '{high_label}' not found in DataFrame.")
    if low_label not in df.columns:
        raise KeyError(f"Column '{low_label}' not found in DataFrame.")
    if len(df) < window:
        raise ValueError(f"DataFrame length ({len(df)}) is less than the specified window ({window}) for Price Channel calculation.")

    if not inplace:
        df = df.copy()

    high_channel_col = 'price_channel_high'
    low_channel_col = 'price_channel_low'

    df[high_channel_col] = df[high_label].rolling(window=window).max()
    df[low_channel_col] = df[low_label].rolling(window=window).min()

    if inplace:
        return None
    else:
        return df


def calculate_atr(df: pd.DataFrame, window: int = 14,
                  high_label: str = 'high_price', low_label: str = 'low_price', close_label: str = 'close_price',
                  inplace: bool = False):
    """
    Calculate Average True Range (ATR) using Simple Moving Average.

    ATR measures market volatility. This implementation uses SMA for averaging the True Range.
    Adds a column named 'atr'.

    Args:
        df (pd.DataFrame): Input DataFrame with OHLC data.
        window (int): The period for the ATR calculation. Defaults to 14.
        high_label (str): Column name for high prices. Defaults to 'high_price'.
        low_label (str): Column name for low prices. Defaults to 'low_price'.
        close_label (str): Column name for close prices. Defaults to 'close_price'.
        inplace (bool): If True, modify the DataFrame in place. Otherwise, return a modified copy. Defaults to False.

    Returns:
        pd.DataFrame or None: The DataFrame with the ATR column added, or None if inplace=True.

    Raises:
        ValueError: If DataFrame length is less than window + 1.
        KeyError: If required price columns are not found.
    """
    required_cols = [high_label, low_label, close_label]
    for col in required_cols:
        if col not in df.columns:
            raise KeyError(f"Column '{col}' not found in DataFrame.")

    # Need window + 1 rows to have 1 result from shift() and then 'window' periods for rolling mean
    if len(df) < window + 1:
        raise ValueError(f"DataFrame length ({len(df)}) is less than the required window + 1 ({window + 1}) for ATR calculation.")

    if not inplace:
        df = df.copy()

    # Calculate True Range components
    tr1 = abs(df[high_label] - df[low_label])
    tr2 = abs(df[high_label] - df[close_label].shift())
    tr3 = abs(df[low_label] - df[close_label].shift())

    # Calculate True Range (max of the three components)
    true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1, skipna=False) # Use concat for clarity

    # Calculate ATR using Simple Moving Average
    atr_col_name = 'atr'
    df[atr_col_name] = true_range.ewm(alpha=1/window, adjust=False).mean()

    if inplace:
        return None
    else:
        return df


def calculate_stochastic(df: pd.DataFrame, k_window: int = 14, d_window: int = 3,
                         high_label: str = 'high_price', low_label: str = 'low_price', close_label: str = 'close_price',
                         inplace: bool = False):
    """
    Calculate Stochastic Oscillator (%K and %D).

    Compares the closing price to its price range over k_window periods.
    %D is the SMA of %K over d_window periods.
    Adds columns 'stoch_%k' and 'stoch_%d'.

    Args:
        df (pd.DataFrame): Input DataFrame with OHLC data.
        k_window (int): The look-back period for %K calculation. Defaults to 14.
        d_window (int): The smoothing period for %D calculation (SMA of %K). Defaults to 3.
        high_label (str): Column name for high prices. Defaults to 'high_price'.
        low_label (str): Column name for low prices. Defaults to 'low_price'.
        close_label (str): Column name for close prices. Defaults to 'close_price'.
        inplace (bool): If True, modify the DataFrame in place. Otherwise, return a modified copy. Defaults to False.

    Returns:
        pd.DataFrame or None: The DataFrame with Stochastic columns added, or None if inplace=True.

    Raises:
        ValueError: If DataFrame length is less than k_window.
        KeyError: If required price columns are not found.
    """
    required_cols = [high_label, low_label, close_label]
    for col in required_cols:
        if col not in df.columns:
            raise KeyError(f"Column '{col}' not found in DataFrame.")

    # Need at least k_window periods for the rolling min/max
    if len(df) < k_window:
        raise ValueError(f"DataFrame length ({len(df)}) is less than the specified k_window ({k_window}) for Stochastic calculation.")

    if not inplace:
        df = df.copy()

    # Calculate rolling min and max
    low_min = df[low_label].rolling(window=k_window).min()
    high_max = df[high_label].rolling(window=k_window).max()

    # Calculate %K
    denominator = high_max - low_min
    # Avoid division by zero: if range is zero, %K is typically considered 0 or 100
    # Setting denominator to a small number pushes %K towards 0 if Close == LowMin,
    denominator = denominator.replace(0, 0.000001) # Prevent division by zero

    k_col_name = 'stoch_k'
    df[k_col_name] = 100 * ((df[close_label] - low_min) / denominator)
    # Optional: Clip values to ensure they are strictly between 0 and 100 if needed
    df[k_col_name] = df[k_col_name].clip(0, 100)

    # Calculate %D (SMA of %K)
    d_col_name = 'stoch_d'
    # Need at least d_window of %K values for the %D SMA
    df[d_col_name] = df[k_col_name].rolling(window=d_window).mean()

    if inplace:
        return None
    else:
        return df


def calculate_obv(df: pd.DataFrame, close_label: str = 'close_price', volume_label: str = 'volume',
                  inplace: bool = False):
    """
    Calculate On-Balance Volume (OBV).

    Measures buying and selling pressure as a cumulative indicator,
    adding volume on up days and subtracting it on down days.
    Adds a column named 'obv'.

    Args:
        df (pd.DataFrame): Input DataFrame with Close prices and Volume.
        close_label (str): Column name for close prices. Defaults to 'close_price'.
        volume_label (str): Column name for volume. Defaults to 'volume'.
        inplace (bool): If True, modify the DataFrame in place. Otherwise, return a modified copy. Defaults to False.

    Returns:
        pd.DataFrame or None: The DataFrame with the OBV column added, or None if inplace=True.

    Raises:
        ValueError: If DataFrame length is less than 2.
        KeyError: If required columns (close, volume) are not found.
    """
    required_cols = [close_label, volume_label]
    for col in required_cols:
        if col not in df.columns:
            raise KeyError(f"Column '{col}' not found in DataFrame.")

    # Need at least 2 rows to calculate price difference
    if len(df) < 2:
        raise ValueError(f"DataFrame length ({len(df)}) must be at least 2 for OBV calculation.")

    if not inplace:
        df = df.copy()

    # Calculate price changes
    price_change = df[close_label].diff()

    # Calculate OBV steps using numpy.where for efficiency
    # Assign volume, negative volume, or 0 based on price change
    obv_change = np.where(
        price_change > 0,
        df[volume_label],
        np.where(
            price_change < 0,
            -df[volume_label],
            0 # No change in OBV if price is unchanged
        )
    )

    # Calculate cumulative OBV
    obv_col_name = 'obv'
    # Set first value to 0 (or df[volume_label].iloc[0] if preferred convention)
    # The diff makes the first price_change NaN, so obv_change[0] will be 0 using np.where above.
    df[obv_col_name] = obv_change.cumsum()
    # Fill the first NaN value resulting from cumsum starting with 0 or first volume
    df[obv_col_name] = df[obv_col_name].fillna(0)

    if inplace:
        return None
    else:
        return df


def calculate_vwap(df: pd.DataFrame, reset_period: str = 'D',
                   high_label: str = 'high_price', low_label: str = 'low_price',
                   close_label: str = 'close_price', volume_label: str = 'volume',
                   time_col_label: str = 'open_time',
                   inplace: bool = False):
    """
    Calculate Volume Weighted Average Price (VWAP), resetting periodically.

    Calculates VWAP based on typical price and volume, resetting the cumulative calculation
    based on the specified frequency (e.g., daily).
    Adds a column named 'vwap'.

    Args:
        df (pd.DataFrame): Input DataFrame. Must have a DatetimeIndex or a specified `time_col_label`
                           that can be converted to datetime, plus HLC and Volume columns.
        reset_period (str): Frequency to reset VWAP calculation (e.g., 'D', 'W', 'M'). Defaults to 'D'.
        high_label (str): Column name for high prices. Defaults to 'high_price'.
        low_label (str): Column name for low prices. Defaults to 'low_price'.
        close_label (str): Column name for close prices. Defaults to 'close_price'.
        volume_label (str): Column name for volume. Defaults to 'volume'.
        time_col_label (str, optional): Column name for datetime information if the index is not
                                       already a DatetimeIndex. Defaults to None.
        inplace (bool): If True, modify the DataFrame in place. Otherwise, return a modified copy. Defaults to False.

    Returns:
        pd.DataFrame or None: DataFrame with VWAP column added, or None if inplace=True.

    Raises:
        TypeError: If the DataFrame index is not DatetimeIndex and `time_col_label` is not provided or invalid.
        ValueError: If DataFrame is empty.
        KeyError: If required HLC, Volume, or time columns are not found.
    """
    if df.empty:
        raise ValueError("Input DataFrame cannot be empty for VWAP calculation.")

    required_cols = [high_label, low_label, close_label, volume_label]
    if time_col_label and not isinstance(df.index, pd.DatetimeIndex):
         required_cols.append(time_col_label)

    for col in required_cols:
        if col not in df.columns:
            raise KeyError(f"Required column '{col}' not found in DataFrame.")

    if not inplace:
        df_vwap = df.copy() # Work on a copy if not inplace
    else:
        df_vwap = df # Work directly on df if inplace

    original_index = df_vwap.index # Store original index if modification needed
    datetime_index_created = False

    # Ensure datetime index for reset_period grouping
    if not isinstance(df_vwap.index, pd.DatetimeIndex):
        if time_col_label:
            try:
                df_vwap[time_col_label] = pd.to_datetime(df_vwap[time_col_label])
                df_vwap.set_index(time_col_label, inplace=True, drop=False) # Keep column if needed
                datetime_index_created = True
            except Exception as e:
                raise TypeError(f"Could not convert column '{time_col_label}' to DatetimeIndex: {e}")
        else:
            raise TypeError("DataFrame index must be DatetimeIndex or 'time_col_label' must be provided.")

    # Calculate typical price and price*volume
    typical_price = (df_vwap[high_label] + df_vwap[low_label] + df_vwap[close_label]) / 3
    pv = typical_price * df_vwap[volume_label]

    # Group by the reset period and calculate cumulative sums
    grouper = pd.Grouper(freq=reset_period)
    cumulative_pv = pv.groupby(grouper).cumsum()
    cumulative_volume = df_vwap[volume_label].groupby(grouper).cumsum()

    # Calculate VWAP, handle potential division by zero if volume is zero at period start
    vwap_col_name = 'vwap'
    # Replace 0 volume with NaN before division to avoid inf/-inf result, yields NaN instead
    df_vwap[vwap_col_name] = cumulative_pv / cumulative_volume.replace(0, np.nan)

    # Restore original index if we created a temporary one
    if datetime_index_created:
        # Decide whether to keep the time column or drop the index based on user expectation
        # Simplest is often to reset to the original index type/state
        df_vwap.index = original_index
        # If the original index was NOT the time col, drop the time col if it wasn't there before
        if time_col_label not in df.columns:
             df_vwap.drop(columns=[time_col_label], inplace=True, errors='ignore')


    if inplace:
        # If inplace=True, the modifications were made directly on df (via df_vwap reference)
        # We need to handle the case where df_vwap might be a copy OR the original df
        # If it was a copy (which only happens if inplace=False), this path shouldn't be taken.
        # If inplace=True, df_vwap IS df, so modifications are already there. We just need to return None.
         if not datetime_index_created and df_vwap is not df:
             # This case should theoretically not happen with current logic, but as safety:
              df[vwap_col_name] = df_vwap[vwap_col_name] # Copy result back if somehow df_vwap was a copy
         return None
    else:
         # If inplace=False, df_vwap is the modified copy
         return df_vwap


def calculate_keltner_channels(df: pd.DataFrame, ema_window: int = 20, atr_window: int = 10, multiplier: float = 2.0,
                             high_label: str = 'high_price', low_label: str = 'low_price',
                             close_label: str = 'close_price',
                             inplace: bool = False):
    """
    Calculate Keltner Channels using EMA and ATR.

    Adds columns: 'keltner_middle', 'keltner_upper', 'keltner_lower'.
    Relies on calculate_ema and calculate_atr functions being available and consistent.

    Args:
        df (pd.DataFrame): Input DataFrame with OHLC data.
        ema_window (int): The period for the middle line EMA. Defaults to 20.
        atr_window (int): The period for the ATR calculation. Defaults to 10.
        multiplier (float): The multiplier for the ATR to set channel width. Defaults to 2.0.
        high_label (str): Column name for high prices. Defaults to 'high_price'.
        low_label (str): Column name for low prices. Defaults to 'low_price'.
        close_label (str): Column name for close prices. Defaults to 'close_price'.
        inplace (bool): If True, modify the DataFrame in place. Otherwise, return a modified copy. Defaults to False.

    Returns:
        pd.DataFrame or None: DataFrame with Keltner Channel columns added, or None if inplace=True.

    Raises:
        ValueError: If DataFrame length is insufficient for underlying EMA/ATR calculations.
        KeyError: If required price columns are not found (checked by underlying functions).
        AttributeError: If calculate_ema or calculate_atr functions are not available.
    """
    if not inplace:
        df = df.copy() # Start with a copy if not modifying in place

    # Calculate EMA - Pass parameters and respect inplace
    ema_series = calculate_ema(df, span=ema_window, label=close_label, inplace=False)[f'ema_{ema_window}']

    # Calculate ATR - Pass parameters and respect inplace
    atr_series = calculate_atr(df, window=atr_window, high_label=high_label, low_label=low_label,
                               close_label=close_label, inplace=False)['atr']


    # Define column names
    middle_col = 'keltner_middle'
    upper_col = 'keltner_upper'
    lower_col = 'keltner_lower'

    # Calculate Keltner Channels
    df[middle_col] = ema_series
    df[upper_col] = ema_series + multiplier * atr_series
    df[lower_col] = ema_series - multiplier * atr_series

    if inplace:
        return None
    else:
        return df


def calculate_cci(df: pd.DataFrame, window: int = 20, constant: float = 0.015,
                  high_label: str = 'high_price', low_label: str = 'low_price', close_label: str = 'close_price',
                  inplace: bool = False):
    """
    Calculate Commodity Channel Index (CCI).

    Measures the current price level relative to an average price level over a given period.
    Adds a column named 'cci'.

    Args:
        df (pd.DataFrame): Input DataFrame with OHLC data.
        window (int): The period for CCI calculation. Defaults to 20.
        constant (float): The constant used in the CCI calculation (typically 0.015). Defaults to 0.015.
        high_label (str): Column name for high prices. Defaults to 'high_price'.
        low_label (str): Column name for low prices. Defaults to 'low_price'.
        close_label (str): Column name for close prices. Defaults to 'close_price'.
        inplace (bool): If True, modify the DataFrame in place. Otherwise, return a modified copy. Defaults to False.

    Returns:
        pd.DataFrame or None: DataFrame with CCI column added, or None if inplace=True.

    Raises:
        ValueError: If DataFrame length is less than the specified window.
        KeyError: If required price columns are not found.
    """
    required_cols = [high_label, low_label, close_label]
    for col in required_cols:
        if col not in df.columns:
            raise KeyError(f"Column '{col}' not found in DataFrame.")

    if len(df) < window:
        raise ValueError(f"DataFrame length ({len(df)}) is less than the specified window ({window}) for CCI calculation.")

    if not inplace:
        df = df.copy()

    # Calculate Typical Price
    typical_price = (df[high_label] + df[low_label] + df[close_label]) / 3

    # Simple Moving Average of Typical Price
    tp_sma = typical_price.rolling(window=window).mean()

    # Mean Absolute Deviation of Typical Price (using updated method)
    mad = typical_price.rolling(window=window).apply(
        lambda x: np.abs(x - x.mean()).mean(), raw=True # raw=True might offer speedup
    )

    # Calculate CCI, handle division by zero in MAD
    cci_col_name = 'cci'
    # Replace 0 MAD with NaN before division to avoid inf/-inf, yields NaN instead
    df[cci_col_name] = (typical_price - tp_sma) / (constant * mad.replace(0, np.nan))

    if inplace:
        return None
    else:
        return df


def calculate_williams_r(df: pd.DataFrame, window: int = 14,
                       high_label: str = 'high_price', low_label: str = 'low_price', close_label: str = 'close_price',
                       inplace: bool = False):
    """
    Calculate Williams %R momentum indicator.

    Measures overbought/oversold levels relative to the high/low range over the window.
    Adds a column named 'williams_r'. Values range from -100 to 0.

    Args:
        df (pd.DataFrame): Input DataFrame with OHLC data.
        window (int): The look-back period. Defaults to 14.
        high_label (str): Column name for high prices. Defaults to 'high_price'.
        low_label (str): Column name for low prices. Defaults to 'low_price'.
        close_label (str): Column name for close prices. Defaults to 'close_price'.
        inplace (bool): If True, modify the DataFrame in place. Otherwise, return a modified copy. Defaults to False.

    Returns:
        pd.DataFrame or None: DataFrame with Williams %R column added, or None if inplace=True.

    Raises:
        ValueError: If DataFrame length is less than the specified window.
        KeyError: If required price columns are not found.
    """
    required_cols = [high_label, low_label, close_label]
    for col in required_cols:
        if col not in df.columns:
            raise KeyError(f"Column '{col}' not found in DataFrame.")

    if len(df) < window:
        raise ValueError(f"DataFrame length ({len(df)}) is less than the specified window ({window}) for Williams %R calculation.")

    if not inplace:
        df = df.copy()

    # Rolling High and Low
    highest_high = df[high_label].rolling(window=window).max()
    lowest_low = df[low_label].rolling(window=window).min()

    # Calculate Williams %R
    denominator = highest_high - lowest_low
    # Replace 0 denominator with NaN before division to avoid inf/-inf, yields NaN instead
    denominator = denominator.replace(0, np.nan)
    wr_col_name = 'williams_r'
    df[wr_col_name] = -100 * (highest_high - df[close_label]) / denominator

    if inplace:
        return None
    else:
        return df


def calculate_ichimoku(df: pd.DataFrame, tenkan_window: int = 9, kijun_window: int = 26,
                       senkou_span_b_window: int = 52, displacement: int = 26, include_span: bool = False,
                       high_label: str = 'high_price', low_label: str = 'low_price', close_label: str = 'close_price',
                       inplace: bool = False):
    """
    Calculate Ichimoku Cloud components.

    Adds columns: 'ichimoku_tenkan_sen', 'ichimoku_kijun_sen', 'ichimoku_senkou_span_a',
                  'ichimoku_senkou_span_b', 'ichimoku_chikou_span'.

    Args:
        df (pd.DataFrame): Input DataFrame with OHLC data.
        tenkan_window (int): Period for Tenkan-sen (Conversion Line). Defaults to 9.
        kijun_window (int): Period for Kijun-sen (Base Line). Defaults to 26.
        senkou_span_b_window (int): Period for Senkou Span B. Defaults to 52.
        displacement (int): Forward displacement for Senkou Spans, backward for Chikou Span. Defaults to 26.
        include_span (bool): If True, include 'ichimoku_senkou_span_a', 'ichimoku_senkou_span_b', 'ichimoku_chikou_span' columns. Defaults to False.
        high_label (str): Column name for high prices. Defaults to 'high_price'.
        low_label (str): Column name for low prices. Defaults to 'low_price'.
        close_label (str): Column name for close prices. Defaults to 'close_price'.
        inplace (bool): If True, modify the DataFrame in place. Otherwise, return a modified copy. Defaults to False.

    Returns:
        pd.DataFrame or None: DataFrame with Ichimoku columns added, or None if inplace=True.

    Raises:
        ValueError: If DataFrame length is less than the maximum required window.
        KeyError: If required price columns are not found.
    """
    required_cols = [high_label, low_label, close_label]
    for col in required_cols:
        if col not in df.columns:
            raise KeyError(f"Column '{col}' not found in DataFrame.")

    max_window = max(tenkan_window, kijun_window, senkou_span_b_window)
    if len(df) < max_window:
        raise ValueError(f"DataFrame length ({len(df)}) is less than the max required window ({max_window}) for Ichimoku calculation.")

    if not inplace:
        df = df.copy()

    # Tenkan-sen (Conversion Line)
    tenkan_high = df[high_label].rolling(window=tenkan_window).max()
    tenkan_low = df[low_label].rolling(window=tenkan_window).min()
    tenkan_col = 'ichimoku_tenkan_sen'
    df[tenkan_col] = (tenkan_high + tenkan_low) / 2

    # Kijun-sen (Base Line)
    kijun_high = df[high_label].rolling(window=kijun_window).max()
    kijun_low = df[low_label].rolling(window=kijun_window).min()
    kijun_col = 'ichimoku_kijun_sen'
    df[kijun_col] = (kijun_high + kijun_low) / 2

    if include_span:
        # Senkou Span A (Leading Span A) - Displaced forward
        senkou_a_col = 'ichimoku_senkou_span_a'
        df[senkou_a_col] = ((df[tenkan_col] + df[kijun_col]) / 2).shift(displacement)

        # Senkou Span B (Leading Span B) - Displaced forward
        senkou_b_high = df[high_label].rolling(window=senkou_span_b_window).max()
        senkou_b_low = df[low_label].rolling(window=senkou_span_b_window).min()
        senkou_b_col = 'ichimoku_senkou_span_b'
        df[senkou_b_col] = ((senkou_b_high + senkou_b_low) / 2).shift(displacement)

        # Chikou Span (Lagging Span) - Displaced backward
        chikou_col = 'ichimoku_chikou_span'
        df[chikou_col] = df[close_label].shift(-displacement)

    if inplace:
        return None
    else:
        return df


# Mapping from configuration keys to calculation functions
INDICATOR_FUNCTION_MAP = {
    'sma': calculate_sma,
    'ema': calculate_ema,
    'rsi': calculate_rsi,
    'macd': calculate_macd,
    'bollinger': calculate_bollinger_bands,
    'price_channel': calculate_price_channel,
    'atr': calculate_atr,
    'stochastic': calculate_stochastic,
    'obv': calculate_obv,
    'vwap': calculate_vwap,
    'keltner': calculate_keltner_channels,
    'cci': calculate_cci,
    'williams_r': calculate_williams_r,
    'ichimoku': calculate_ichimoku
}


def add_indicators(
    df: pd.DataFrame,
    indicators_config: dict,
    high_label: str = 'high_price',
    low_label: str = 'low_price',
    close_label: str = 'close_price',
    volume_label: str = 'volume',
    time_col_label: str = 'open_time'
) -> pd.DataFrame:
    """
    Adds multiple technical indicators to a DataFrame based on a configuration dictionary.

    This function acts as a wrapper around individual 'calculate_*' functions.
    It processes a configuration dictionary to determine which indicators to add
    and allows specifying parameters for each.

    Args:
        df (pd.DataFrame):
            Input DataFrame containing OHLCV data. Column names should match
            the label arguments or defaults.
        indicators_config (dict):
            Configuration dictionary specifying which indicators to calculate and their parameters.
            Keys should be indicator names (e.g., 'sma', 'rsi', 'macd').
            Values can be:
                - True: Calculate the indicator with its default parameters.
                - dict: Calculate the indicator with the specified parameters.
                - list[dict]: Calculate the indicator multiple times with different parameters
                              (e.g., `{'sma': [{'window': 10}, {'window': 50}]}`).
        high_label (str): Column name for high prices. Defaults to 'high_price'.
        low_label (str): Column name for low prices. Defaults to 'low_price'.
        close_label (str): Column name for close prices. Defaults to 'close_price'.
        volume_label (str): Column name for volume. Defaults to 'volume'.
        time_col_label (str, optional): Column name for datetime info if index isn't DatetimeIndex
                                       (primarily needed for VWAP). Defaults to None.

    Returns:
        pd.DataFrame: A new DataFrame with the original data and the calculated indicator columns added.

    Raises:
        ValueError: If input DataFrame is empty or required configurations lead to errors.
        KeyError: If specified label columns are missing in the input DataFrame.
        AttributeError: If an indicator function specified in the map is not defined.
        TypeError: If parameters passed to indicator functions are incorrect.

    Example Usage:
        config = {
            'sma': [{'window': 10}, {'window': 50}], # Two SMAs
            'rsi': True,                             # Default RSI
            'macd': {'fast_period': 8, 'slow_period': 18}, # Custom MACD periods
            'bollinger': True,
            'atr': True,
            'keltner': True, # Note: Relies on default EMA(20)/ATR(10) from underlying functions
            'obv': True,
            'cci': True,
        }
        df_with_indicators = add_indicators(ohlcv_data, config)
    """
    if df.empty:
        raise ValueError("Input DataFrame cannot be empty.")

    # Work on a copy to avoid modifying the original DataFrame
    df_out = df.copy()

    # Define common arguments based on provided labels
    # These will be passed potentially to all indicator functions
    common_args = {
        'high_label': high_label,
        'low_label': low_label,
        'close_label': close_label,
        'volume_label': volume_label,
        'time_col_label': time_col_label,
    }

    # Iterate through the requested indicators in the configuration
    for indicator_key, config_value in indicators_config.items():
        if indicator_key not in INDICATOR_FUNCTION_MAP:
            print(f"Warning: Unknown indicator key '{indicator_key}' in configuration. Skipping.")
            continue

        try:
            calculation_function = INDICATOR_FUNCTION_MAP[indicator_key]
        except KeyError:
             # This case is technically covered above, but added for safety
            print(f"Warning: Indicator function for '{indicator_key}' not found in map. Skipping.")
            continue
        except AttributeError:
             print(f"Warning: Function mapped for '{indicator_key}' is not defined. Skipping.")
             continue

        # Standardize the configuration value to be a list of parameter dictionaries
        if config_value is True:
            # Use default parameters defined in the calculation function
            param_sets = [{}]
        elif isinstance(config_value, dict):
            # Single set of specific parameters provided
            param_sets = [config_value]
        elif isinstance(config_value, list) and all(isinstance(p, dict) for p in config_value):
            # List of parameter sets provided (e.g., multiple SMAs)
            param_sets = config_value
        elif config_value is False or config_value is None:
             # Explicitly skip if config is False or None
             continue
        else:
            print(f"Warning: Invalid configuration value type for '{indicator_key}': {type(config_value)}. Expected bool, dict, or list[dict]. Skipping.")
            continue

        # Apply the indicator calculation for each parameter set
        for params in param_sets:
            # Prepare arguments for the specific function call
            # Start with common args relevant to OHLCV, then add/override with specific params
            current_args = common_args.copy()
            current_args.update(params) # Add/override with specific params from config

            # Filter arguments to only those accepted by the specific calculation function
            sig = inspect.signature(calculation_function)
            valid_params = sig.parameters
            function_args = {k: v for k, v in current_args.items() if k in valid_params}

            # Always call underlying functions with inplace=False
            function_args['inplace'] = False

            try:
                # Apply calculation - result is a new DataFrame with the indicator added
                df_out = calculation_function(df_out, **function_args)
                if df_out is None:
                     # This should not happen if underlying functions respect inplace=False
                     raise RuntimeError(f"Indicator function {indicator_key} returned None unexpectedly when inplace=False.")

            except (ValueError, KeyError, TypeError, AttributeError, RuntimeError) as e:
                # Catch potential errors from individual functions
                print(f"Error calculating indicator '{indicator_key}' with params {params}: {type(e).__name__} - {e}")
                # Optionally re-raise if you want processing to stop on any error
                # raise e
            except Exception as e:
                 # Catch any other unexpected errors
                 print(f"An unexpected error occurred calculating indicator '{indicator_key}' with params {params}: {type(e).__name__} - {e}")
                 # raise e

    return df_out


if __name__ == "__main__":
    from src.utils.extract_data import get_klines_df
    from src.config.base import INDICATORS_CONFIG

    df = get_klines_df("BTCUSDT", "15m", 100)

    df_with_indicators = add_indicators(df, INDICATORS_CONFIG)
