import datetime
import pandas as pd

from dataclasses import dataclass, asdict
from typing import Any


@dataclass
class LLMResponse:
    response_time: Any
    choice: int
    confidence: float
    reasoning: str
    order: str

    def __post_init__(self):
        if isinstance(self.response_time, pd.Timestamp):
            self.response_time = self.response_time.to_pydatetime()
        elif isinstance(self.response_time, str):
            self.response_time = datetime.datetime.fromisoformat(self.response_time)

    def dict(self):
        data = asdict(self)
        data = {k: v for k, v in data.items() if v is not None}

        dt_keys = ['response_time']
        float_keys = ['confidence']

        for key in dt_keys:
            if key in data:
                data[key] = data[key].strftime('%Y-%m-%d %H:%M:%S')

        for key in float_keys:
            if key in data:
                data[key] = round(data[key], 3)
        return data
