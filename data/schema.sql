DROP TABLE IF EXISTS swings_1m;
DROP TABLE IF EXISTS swings_15m;
DROP TABLE IF EXISTS swings_1h;
DROP TABLE IF EXISTS swings_4h;
DROP TABLE IF EXISTS swings_1d;

DROP TABLE IF EXISTS klines_1m;
DROP TABLE IF EXISTS klines_15m;
DROP TABLE IF EXISTS klines_1h;
DROP TABLE IF EXISTS klines_4h;
DROP TABLE IF EXISTS klines_1d;

DROP TABLE IF EXISTS orders;

DROP TABLE IF EXISTS open_positions;

DROP TABLE IF EXISTS llm_response_history;

-- ------------------------------------------------------ --

CREATE TABLE IF NOT EXISTS klines_1m (
    open_time DATETIME PRIMARY KEY,
    open_price FLOAT,
    high_price FLOAT,
    low_price FLOAT,
    close_price FLOAT,
    volume FLOAT,
    close_time DATETIME,
    quote_asset_volume FLOAT,
    number_of_trades INT,
    taker_buy_base_asset_volume FLOAT,
    taker_buy_quote_asset_volume FLOAT,
    status TEXT,
    buy_volume_ratio FLOAT,
    sell_volume_ratio FLOAT,

    sma_14 FLOAT
    sma_50 FLOAT
    sma_99 FLOAT
    ema_8 FLOAT
    ema_20 FLOAT
    ema_50 FLOAT
    rsi FLOAT
    macd FLOAT
    macd_signal FLOAT
    macd_hist FLOAT
    bb_middle FLOAT
    bb_upper FLOAT
    bb_lower FLOAT
    price_channel_high FLOAT
    price_channel_low FLOAT
    atr FLOAT
    keltner_middle FLOAT
    keltner_upper FLOAT
    keltner_lower FLOAT
    stoch_k FLOAT
    stoch_d FLOAT
    obv FLOAT
    cci FLOAT
    williams_r FLOAT
    ichimoku_tenkan_sen FLOAT
    ichimoku_kijun_sen FLOAT
    ichimoku_senkou_span_a FLOAT
    ichimoku_senkou_span_b FLOAT
    ichimoku_chikou_span FLOAT
    vwap FLOAT

    trade_spike INT,
    -- created_at DATETIME DEFAULT (datetime('now')),
    -- updated_at DATETIME DEFAULT (datetime('now')),
    UNIQUE (open_time, close_time)
);

CREATE TABLE IF NOT EXISTS klines_15m (
    open_time DATETIME PRIMARY KEY,
    open_price FLOAT,
    high_price FLOAT,
    low_price FLOAT,
    close_price FLOAT,
    volume FLOAT,
    close_time DATETIME,
    quote_asset_volume FLOAT,
    number_of_trades INT,
    taker_buy_base_asset_volume FLOAT,
    taker_buy_quote_asset_volume FLOAT,
    status TEXT,
    buy_volume_ratio FLOAT,
    sell_volume_ratio FLOAT,

    sma_14 FLOAT
    sma_50 FLOAT
    sma_99 FLOAT
    ema_8 FLOAT
    ema_20 FLOAT
    ema_50 FLOAT
    rsi FLOAT
    macd FLOAT
    macd_signal FLOAT
    macd_hist FLOAT
    bb_middle FLOAT
    bb_upper FLOAT
    bb_lower FLOAT
    price_channel_high FLOAT
    price_channel_low FLOAT
    atr FLOAT
    keltner_middle FLOAT
    keltner_upper FLOAT
    keltner_lower FLOAT
    stoch_k FLOAT
    stoch_d FLOAT
    obv FLOAT
    cci FLOAT
    williams_r FLOAT
    ichimoku_tenkan_sen FLOAT
    ichimoku_kijun_sen FLOAT
    ichimoku_senkou_span_a FLOAT
    ichimoku_senkou_span_b FLOAT
    ichimoku_chikou_span FLOAT
    vwap FLOAT

    trade_spike INT,
    -- created_at DATETIME DEFAULT (datetime('now')),
    -- updated_at DATETIME DEFAULT (datetime('now')),
    UNIQUE (open_time, close_time)
);

CREATE TABLE IF NOT EXISTS klines_1h (
    open_time DATETIME PRIMARY KEY,
    open_price FLOAT,
    high_price FLOAT,
    low_price FLOAT,
    close_price FLOAT,
    volume FLOAT,
    close_time DATETIME,
    quote_asset_volume FLOAT,
    number_of_trades INT,
    taker_buy_base_asset_volume FLOAT,
    taker_buy_quote_asset_volume FLOAT,
    status TEXT,
    buy_volume_ratio FLOAT,
    sell_volume_ratio FLOAT,

    sma_14 FLOAT
    sma_50 FLOAT
    sma_99 FLOAT
    ema_8 FLOAT
    ema_20 FLOAT
    ema_50 FLOAT
    rsi FLOAT
    macd FLOAT
    macd_signal FLOAT
    macd_hist FLOAT
    bb_middle FLOAT
    bb_upper FLOAT
    bb_lower FLOAT
    price_channel_high FLOAT
    price_channel_low FLOAT
    atr FLOAT
    keltner_middle FLOAT
    keltner_upper FLOAT
    keltner_lower FLOAT
    stoch_k FLOAT
    stoch_d FLOAT
    obv FLOAT
    cci FLOAT
    williams_r FLOAT
    ichimoku_tenkan_sen FLOAT
    ichimoku_kijun_sen FLOAT
    ichimoku_senkou_span_a FLOAT
    ichimoku_senkou_span_b FLOAT
    ichimoku_chikou_span FLOAT
    vwap FLOAT

    trade_spike INT,
    -- created_at DATETIME DEFAULT (datetime('now')),
    -- updated_at DATETIME DEFAULT (datetime('now')),
    UNIQUE (open_time, close_time)
);

CREATE TABLE IF NOT EXISTS klines_4h (
    open_time DATETIME PRIMARY KEY,
    open_price FLOAT,
    high_price FLOAT,
    low_price FLOAT,
    close_price FLOAT,
    volume FLOAT,
    close_time DATETIME,
    quote_asset_volume FLOAT,
    number_of_trades INT,
    taker_buy_base_asset_volume FLOAT,
    taker_buy_quote_asset_volume FLOAT,
    status TEXT,
    buy_volume_ratio FLOAT,
    sell_volume_ratio FLOAT,

    sma_14 FLOAT
    sma_50 FLOAT
    sma_99 FLOAT
    ema_8 FLOAT
    ema_20 FLOAT
    ema_50 FLOAT
    rsi FLOAT
    macd FLOAT
    macd_signal FLOAT
    macd_hist FLOAT
    bb_middle FLOAT
    bb_upper FLOAT
    bb_lower FLOAT
    price_channel_high FLOAT
    price_channel_low FLOAT
    atr FLOAT
    keltner_middle FLOAT
    keltner_upper FLOAT
    keltner_lower FLOAT
    stoch_k FLOAT
    stoch_d FLOAT
    obv FLOAT
    cci FLOAT
    williams_r FLOAT
    ichimoku_tenkan_sen FLOAT
    ichimoku_kijun_sen FLOAT
    ichimoku_senkou_span_a FLOAT
    ichimoku_senkou_span_b FLOAT
    ichimoku_chikou_span FLOAT
    vwap FLOAT

    trade_spike INT,
    -- created_at DATETIME DEFAULT (datetime('now')),
    -- updated_at DATETIME DEFAULT (datetime('now')),
    UNIQUE (open_time, close_time)
);

CREATE TABLE IF NOT EXISTS klines_1d (
    open_time DATETIME PRIMARY KEY,
    open_price FLOAT,
    high_price FLOAT,
    low_price FLOAT,
    close_price FLOAT,
    volume FLOAT,
    close_time DATETIME,
    quote_asset_volume FLOAT,
    number_of_trades INT,
    taker_buy_base_asset_volume FLOAT,
    taker_buy_quote_asset_volume FLOAT,
    status TEXT,
    buy_volume_ratio FLOAT,
    sell_volume_ratio FLOAT,

    sma_14 FLOAT
    sma_50 FLOAT
    sma_99 FLOAT
    ema_8 FLOAT
    ema_20 FLOAT
    ema_50 FLOAT
    rsi FLOAT
    macd FLOAT
    macd_signal FLOAT
    macd_hist FLOAT
    bb_middle FLOAT
    bb_upper FLOAT
    bb_lower FLOAT
    price_channel_high FLOAT
    price_channel_low FLOAT
    atr FLOAT
    keltner_middle FLOAT
    keltner_upper FLOAT
    keltner_lower FLOAT
    stoch_k FLOAT
    stoch_d FLOAT
    obv FLOAT
    cci FLOAT
    williams_r FLOAT
    ichimoku_tenkan_sen FLOAT
    ichimoku_kijun_sen FLOAT
    ichimoku_senkou_span_a FLOAT
    ichimoku_senkou_span_b FLOAT
    ichimoku_chikou_span FLOAT
    vwap FLOAT

    trade_spike INT,
    -- created_at DATETIME DEFAULT (datetime('now')),
    -- updated_at DATETIME DEFAULT (datetime('now')),
    UNIQUE (open_time, close_time)
);

-- ------------------------------------------------------ --

CREATE TABLE IF NOT EXISTS swings_1m (
    dt DATETIME PRIMARY KEY,
    value FLOAT,
    stype TEXT,
    FOREIGN KEY (dt) REFERENCES klines_1m(open_time) ON DELETE CASCADE,
    UNIQUE (dt)
);

CREATE TABLE IF NOT EXISTS swings_15m (
    dt DATETIME PRIMARY KEY,
    value FLOAT,
    stype TEXT,
    FOREIGN KEY (dt) REFERENCES klines_15m(open_time) ON DELETE CASCADE,
    UNIQUE (dt)
);

CREATE TABLE IF NOT EXISTS swings_1h (
    dt DATETIME PRIMARY KEY,
    value FLOAT,
    stype TEXT,
    FOREIGN KEY (dt) REFERENCES klines_1h(open_time) ON DELETE CASCADE,
    UNIQUE (dt)
);

CREATE TABLE IF NOT EXISTS swings_4h (
    dt DATETIME PRIMARY KEY,
    value FLOAT,
    stype TEXT,
    FOREIGN KEY (dt) REFERENCES klines_4h(open_time) ON DELETE CASCADE,
    UNIQUE (dt)
);

CREATE TABLE IF NOT EXISTS swings_1d (
    dt DATETIME PRIMARY KEY,
    value FLOAT,
    stype TEXT,
    FOREIGN KEY (dt) REFERENCES klines_1d(open_time) ON DELETE CASCADE,
    UNIQUE (dt)
);

-- ------------------------------------------------------ --

-- CREATE TABLE IF NOT EXISTS orders (
--     order_id INT PRIMARY KEY,
--     symbol TEXT,
--     status TEXT,
--     client_order_id TEXT,
--     price FLOAT,
--     avg_price FLOAT,
--     orig_qty FLOAT,
--     executed_qty FLOAT,
--     cum_quote FLOAT,
--     time_in_force TEXT,
--     type TEXT,
--     reduce_only BOOLEAN,
--     close_position BOOLEAN,
--     side TEXT,
--     position_side TEXT,
--     stop_price FLOAT,
--     working_type TEXT,
--     price_protect BOOLEAN,
--     orig_type TEXT,
--     price_match BOOLEAN,
--     self_trade_prevention_mode TEXT,
--     good_till_date TEXT,
--     time DATETIME,
--     update_time DATETIME,
--     UNIQUE (order_id, client_order_id)
-- );

-- ------------------------------------------------------ --

-- Open Positions Table
-- CREATE TABLE IF NOT EXISTS open_positions (
--     symbol TEXT NOT NULL,
--     quantity FLOAT NOT NULL,
--     entry_price FLOAT NOT NULL,
--     position_side TEXT NOT NULL,
--     quantity_usd FLOAT NOT NULL,
--     position_open_time DATETIME NOT NULL,
--     break_even_price FLOAT NOT NULL,
--     mark_price FLOAT,
--     unrealized_profit FLOAT,
--     liquidation_price FLOAT,
--     leverage INTEGER,
--     max_notional_value FLOAT,
--     margin_type TEXT,
--     isolated_margin FLOAT,
--     is_auto_add_margin BOOLEAN,
--     isolated_wallet FLOAT,
--     isolated BOOLEAN,
--     adl_quantile INTEGER,
--     UNIQUE (symbol, position_side)
-- );

-- Closed Positions Table
-- CREATE TABLE IF NOT EXISTS closed_positions (
--     symbol TEXT NOT NULL,
--     quantity FLOAT NOT NULL,
--     entry_price FLOAT NOT NULL,
--     position_side TEXT NOT NULL,
--     quantity_usd FLOAT NOT NULL,
--     position_open_time DATETIME NOT NULL,
--     exit_price FLOAT NOT NULL,
--     pnl FLOAT NOT NULL,
--     position_close_time DATETIME NOT NULL,
--     mark_price FLOAT,
--     unrealized_profit FLOAT,
--     liquidation_price FLOAT,
--     leverage INTEGER,
--     max_notional_value FLOAT,
--     margin_type TEXT,
--     isolated_margin FLOAT,
--     is_auto_add_margin BOOLEAN,
--     isolated_wallet FLOAT,
--     isolated BOOLEAN,
--     adl_quantile INTEGER
-- );

-- ------------------------------------------------------ --

-- CREATE TABLE IF NOT EXISTS llm_response_history (
--     response_time DATETIME,
--     choice INT,
--     confidence FLOAT,
--     reasoning TEXT,
--     `order` JSON,
--     UNIQUE (response_time)
-- );
