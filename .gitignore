# --- Standard Python & Environment Ignores ---

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff: (Removed db.sqlite3*)
*.log
local_settings.py

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook Checkpoints
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
# .python-version

# pipenv
# Pipfile.lock

# poetry
# poetry.lock

# pdm
# pdm.lock
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments & Secrets
.env
.env.*
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Conda environment
.conda

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# Type checker caches
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/
.pytype/

# Cython debug symbols
cython_debug/

# --- Editor / IDE specific ---
# PyCharm
.idea/

# VS Code
.vscode/

# --- Project Specific ---

# SQLite Database Files
# Ignore the specific directory containing the live database
data/sqlite/
# General rules as fallback (might be redundant if above covers all)
*.db
*.db-* # Catches journal files, wal files etc.
*.sqlite3
*.sqlite3-*

# Data files - adjust if some CSVs should be tracked
# *.csv

# PyTorch / ML checkpoints (if applicable)
*.ckpt
# *.yaml # Removed: Too broad, often needed for config files. Add back only if necessary for untracked files.

# Log files (if generated outside standard Django *.log)
*.log
logs/
*.log.*

# Temporary images
data/images

# --- End Project Specific ---