import json
import pandas as pd
import numpy as np

from confluent_kafka import Producer
from prefect import flow, task
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import precision_recall_curve, roc_auc_score, confusion_matrix, auc

from src.config.logs import setup_prefect_logging
from src.core.ml.dataset import prepare_training_dataset, finalize_training_dataset
from src.config.base import DEFAULT_TRAINING_CONFIG,  KAFKA_SERVERS, KAFKA_TOPIC



@task
def collect_data_task(config):
    df = finalize_training_dataset(prepare_training_dataset(**config))
    return df


@task
def train_test_split_task(df, train_ratio=0.9):
    split_idx = int(len(df) * train_ratio)
    X, y = df.drop(columns='target'), df['target']
    X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
    y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
    return X_train, X_test, y_train, y_test


@task
def train_model_task(X_train, y_train):
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    return model


@task
def evaluate_model_task(model, X_test, y_test):
    y_pred_proba = model.predict_proba(X_test)[:, 1]
    y_pred = (y_pred_proba >= 0.5).astype(int)
    precision, recall, thresholds = precision_recall_curve(y_test, y_pred_proba)
    roc_auc = roc_auc_score(y_test, y_pred_proba)
    pr_auc = auc(recall, precision)
    cm = confusion_matrix(y_test, y_pred)

    evaluation_results = {
        "threshold": 0.5,
        "pr_auc": pr_auc,
        "roc_auc": roc_auc,
        "confusion_matrix": cm,
        "feature_importances": pd.Series(
            model.feature_importances_, index=X_test.columns
        ).head(10).sort_values(ascending=False)
    }
    return evaluation_results


@task
def predict_next_kline_task(model, sample, threshold):
    pred_proba = model.predict_proba(sample)[0][1]
    prediction = int(pred_proba >= threshold)

    return {
        "prediction": prediction,
        "confidence": round(float(pred_proba), 2),
        "threshold": round(float(threshold), 2)
    }


@task
def send_prediction_to_kafka_task(symbol, interval, prediction):
    producer = Producer({
        'bootstrap.servers': KAFKA_SERVERS
    })

    message = {
        'type': 'ml_prediction',
        'symbol': symbol,
        'interval': interval
    }
    message.update(prediction)

    producer.produce(KAFKA_TOPIC, json.dumps(message).encode('utf-8'))
    producer.flush()

    return message


@flow
def ml_pipeline_flow(ticker: str, config: dict):
    logger = setup_prefect_logging()

    run_config = config.copy()
    run_config["ticker"] = ticker

    logger.info("Starting ML prediction pipeline...")

    df = collect_data_task(run_config)
    X_train, X_test, y_train, y_test = train_test_split_task(df)
    model = train_model_task(X_train, y_train)
    evaluation_results = evaluate_model_task(model, X_test, y_test)

    sample = X_test.tail(1)
    prediction = predict_next_kline_task(model, sample, evaluation_results['threshold'])

    message = None
    if prediction['confidence'] > 0.6 or prediction['confidence'] < 0.4:

        message = send_prediction_to_kafka_task(ticker, run_config["interval"], prediction)

        logger.info(f"ML prediction sent: {prediction}")
    else:
        logger.info(f"ML prediction skipped: {prediction}")

    return message


if __name__ == "__main__":
    ticker = "BNBUSDT"
    config = DEFAULT_TRAINING_CONFIG.copy()
    pred = ml_pipeline_flow(ticker, config)
    print(pred)
