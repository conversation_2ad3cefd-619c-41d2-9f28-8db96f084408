services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.5.3
    container_name: zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
      - zookeeper-log:/var/lib/zookeeper/log
    networks:
      - shared_network
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:7.5.3
    container_name: kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "29092:29092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - shared_network
    restart: unless-stopped

  kafka-init-topics:
    image: confluentinc/cp-kafka:7.5.3
    container_name: kafka-init-topics
    depends_on:
      kafka:
        condition: service_started
    networks:
      - shared_network
    command: >
      bash -c "
        echo 'Waiting for Kafka to be ready...';
        cub kafka-ready -b kafka:9092 1 30 &&
        /usr/bin/kafka-topics --create --topic signals --partitions 1 --replication-factor 1 --if-not-exists --config retention.ms=3600000 --bootstrap-server kafka:9092 &&
        echo 'Topics created successfully.';
      "
    restart: on-failure


networks:
  shared_network:
    name: app-shared-network
    external: true

volumes:
  kafka-data:
  zookeeper-data:
  zookeeper-log:
