services:
  postgres:
    image: postgres:14
    container_name: prefect-postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-prefect}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-prefectpassword}
      POSTGRES_DB: ${POSTGRES_DB:-prefect}
    volumes:
      - prefect-db-data:/var/lib/postgresql/data
    networks:
      - shared_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $${POSTGRES_USER:-prefect} -d $${POSTGRES_DB:-prefect}"]
      interval: 5s
      timeout: 5s
      retries: 3
    restart: unless-stopped

  prefect-server:
    build:
      context: ../../
      dockerfile: deploy/prefect/Dockerfile
    container_name: prefect-server-ui
    restart: unless-stopped
    command: prefect server start --host 0.0.0.0 --port 4200
    environment:
      PREFECT_API_DATABASE_CONNECTION_URL: "postgresql+asyncpg://${POSTGRES_USER:-prefect}:${POSTGRES_PASSWORD:-prefectpassword}@postgres:5432/${POSTGRES_DB:-prefect}"
      PREFECT_API_URL: "http://localhost:4200/api"
      PREFECT_UI_URL: "http://localhost:4200"
      PREFECT_LOGGING_LEVEL: INFO
      PREFECT_LOGGING_SERVER_LEVEL: INFO
    ports:
      - "4200:4200"
    volumes:
      - prefect-server-data:/root/.prefect
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - shared_network
    healthcheck:
      # Healthcheck should now work reliably once server binds to 0.0.0.0
      test: ["CMD-SHELL", "curl -sf http://localhost:4200/api/health || exit 1"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 5s

  prefect-agent:
    build:
      context: ../../
      dockerfile: deploy/prefect/Dockerfile
    container_name: prefect-agent
    restart: unless-stopped
    command: /app/deploy_and_start.sh
    environment:
      PREFECT_API_URL: "http://prefect-server:4200/api"
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
    env_file:
      - .env
    depends_on:
      prefect-server:
        condition: service_healthy
    networks:
      - shared_network
    volumes:
      - ../../src:/app/src
      - ../../flows:/app/flows

networks:
  shared_network:
    name: app-shared-network
    external: true

volumes:
  prefect-db-data:
  prefect-server-data: