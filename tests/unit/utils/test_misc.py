# filepath: /home/<USER>/crypto-42/tests/utils/test_misc.py
import datetime
import pytest
import pandas as pd
import os
from pathlib import Path
from unittest.mock import patch, MagicMock, Mock

from src.utils.misc import (
    is_valid_iso_datetime, df2dataclass, exception_handler, 
    parallel_execute, calc_swings_and_plot, prepare_indicators_image,
    INTERVAL_LIMIT_MAPPING
)


class TestMisc:
    
    @pytest.fixture
    def mock_dataclass(self):
        """Create a mock dataclass for testing."""
        class TestDataClass:
            def __init__(self, field1, field2):
                self.field1 = field1
                self.field2 = field2
        return TestDataClass
    
    @pytest.fixture
    def sample_df(self):
        """Create a sample dataframe for testing."""
        return pd.DataFrame({
            'field1': [1, 2, 3],
            'field2': ['a', 'b', 'c'],
            'field3': [True, False, True]
        })
    
    @pytest.fixture
    def mock_klines_df(self):
        """Create a mock dataframe that resembles klines data."""
        now = datetime.datetime.now()
        return pd.DataFrame({
            'open_time': [now - datetime.timedelta(minutes=i) for i in range(10, 0, -1)],
            'close_time': [now - datetime.timedelta(minutes=i-1) for i in range(10, 0, -1)],
            'open_price': [100.0 + i for i in range(10)],
            'high_price': [110.0 + i for i in range(10)],
            'low_price': [90.0 + i for i in range(10)],
            'close_price': [105.0 + i for i in range(10)],
            'volume': [1000.0 + i*100 for i in range(10)]
        })
    
    def test_is_valid_iso_datetime_valid(self):
        """Test valid ISO datetime strings."""
        valid_datetimes = [
            "2023-04-14T12:34:56",
            "2023-04-14T12:34:56.789",
            "2023-04-14T12:34:56+00:00",
            "2023-04-14T12:34:56Z"
        ]
        for dt_str in valid_datetimes:
            assert is_valid_iso_datetime(dt_str) is True
    
    def test_is_valid_iso_datetime_invalid(self):
        """Test invalid ISO datetime strings."""
        invalid_datetimes = [
            "2023/04/14 12:34:56",  # Wrong format
            # Note: "2023-04-14" is actually valid in ISO format
            "12:34:56",  # Time only (without date)
            "not a datetime",  # Not a datetime at all
            ""  # Empty string
        ]
        for dt_str in invalid_datetimes:
            assert is_valid_iso_datetime(dt_str) is False
    
    def test_df2dataclass(self, sample_df, mock_dataclass):
        """Test converting DataFrame to a list of dataclass instances."""
        result = df2dataclass(sample_df, mock_dataclass, drop_columns=['field3'])
        
        assert len(result) == 3
        assert isinstance(result[0], mock_dataclass)
        assert result[0].field1 == 1
        assert result[0].field2 == 'a'
        assert result[1].field1 == 2
        assert result[1].field2 == 'b'
    
    def test_df2dataclass_empty(self, mock_dataclass):
        """Test converting an empty DataFrame."""
        empty_df = pd.DataFrame()
        result = df2dataclass(empty_df, mock_dataclass)
        
        assert result == []
    
    def test_exception_handler(self):
        """Test the exception_handler decorator."""
        @exception_handler
        def test_func():
            return "success"
        
        @exception_handler
        def failing_func():
            raise ValueError("Test error")
        
        assert test_func() == "success"
        assert failing_func() is None
    
    def test_parallel_execute(self):
        """Test parallel execution of functions."""
        def func1(x):
            return x * 2
        
        def func2(y):
            return y + 10
        
        functions_with_kwargs = [
            (func1, {'x': 5}),
            (func2, {'y': 7})
        ]
        
        results = parallel_execute(functions_with_kwargs)
        
        assert len(results) == 2
        assert results[0] == 10  # 5 * 2
        assert results[1] == 17  # 7 + 10
    
    def test_parallel_execute_with_error(self):
        """Test parallel execution when one function raises an error."""
        def func1(x):
            return x * 2
        
        def func2(y):
            raise ValueError("Test error")
        
        functions_with_kwargs = [
            (func1, {'x': 5}),
            (func2, {'y': 7})
        ]
        
        results = parallel_execute(functions_with_kwargs)
        
        assert len(results) == 2
        assert results[0] == 10
        assert isinstance(results[1], str)  # Error message as string
        assert "Test error" in results[1]
    
    @patch('src.utils.misc.StructuralSwingsDetector')
    @patch('src.utils.misc.plot_candlesticks')
    @patch('pathlib.Path.exists')
    def test_calc_swings_and_plot(self, mock_exists, mock_plot, mock_detector, mock_klines_df):
        """Test calculating swings and plotting."""
        # Setup mocks
        mock_ssd_instance = Mock()
        mock_detector.return_value = mock_ssd_instance
        mock_ssd_instance.run.return_value = mock_ssd_instance
        mock_ssd_instance.klines_buffer.klines = [1, 2, 3, 4, 5]  # Dummy klines data
        mock_ssd_instance.get_structural_swings.return_value = [{'type': 'high', 'price': 110}, {'type': 'low', 'price': 90}]
        
        mock_exists.return_value = True
        
        # Test function
        result = calc_swings_and_plot(mock_klines_df, 'BTCUSDT', '1h')
        
        # Assertions
        mock_detector.assert_called_once()
        mock_ssd_instance.run.assert_called_once()
        mock_ssd_instance.get_structural_swings.assert_called_once()
        mock_plot.assert_called_once()
        assert isinstance(result, str)
        assert "BTCUSDT" in result
    
    @patch('src.utils.misc.calc_swings_and_plot')
    @patch('src.core.technical_analysis.indicators.add_indicators')
    @patch('src.utils.extract_data.get_klines_df')
    def test_prepare_indicators_image(self, mock_get_klines, mock_add_indicators, mock_calc_swings, mock_klines_df):
        """Test preparing indicator images."""
        # Setup mocks
        mock_get_klines.return_value = mock_klines_df
        mock_add_indicators.return_value = mock_klines_df  # Return the same dataframe with "indicators"
        mock_calc_swings.return_value = "/path/to/image.png"
        
        # Test valid interval
        result = prepare_indicators_image("BTCUSDT", "1h")
        
        # Assertions
        mock_get_klines.assert_called_once_with(
            symbol="BTCUSDT", 
            interval="1h", 
            limit=INTERVAL_LIMIT_MAPPING["1h"] + 100
        )
        mock_add_indicators.assert_called_once()
        mock_calc_swings.assert_called_once()
        assert result == "/path/to/image.png"
    
    def test_prepare_indicators_image_invalid_interval(self):
        """Test preparing indicator images with invalid interval."""
        with pytest.raises(ValueError, match="Invalid interval: invalid_interval"):
            prepare_indicators_image("BTCUSDT", "invalid_interval")
