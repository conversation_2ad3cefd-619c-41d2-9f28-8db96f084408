# import os
# import shutil
# import logging
#
# from src.config.base import IMAGES_PATH, INTERVAL_LIMIT_MAPPING, INDICATORS_CONFIG
# from src.core.technical_analysis.indicators import add_indicators
# from src.core.technical_analysis.data_structures import KLine
# from src.utils.extract_data import get_klines_df, get_balance_df
# from src.utils.image_utils import concat_images
# from src.utils.db_utils import insert_records, is_db_exists
# from src.utils.misc import parallel_execute, calc_swings_and_plot
# from tasks.apply_schema import apply_schema
#
#
# logger = logging.getLogger(__name__)
#
#
# def collect_data(ticker: str, asset: str):
#     functions_with_kwargs = [
#         (get_klines_df, dict(symbol=ticker, interval="1m", limit=INTERVAL_LIMIT_MAPPING["1m"] + 1)),
#         (get_klines_df, dict(symbol=ticker, interval="15m", limit=INTERVAL_LIMIT_MAPPING["15m"] + 1)),
#         (get_klines_df, dict(symbol=ticker, interval="1h", limit=INTERVAL_LIMIT_MAPPING["1h"] + 1)),
#         (get_klines_df, dict(symbol=ticker, interval="4h", limit=INTERVAL_LIMIT_MAPPING["4h"] + 1)),
#         (get_klines_df, dict(symbol=ticker, interval="1d", limit=INTERVAL_LIMIT_MAPPING["1d"] + 1)),
#         (get_balance_df, dict(asset=asset))
#     ]
#     results = parallel_execute(functions_with_kwargs)
#     data_dfs = dict(zip([
#         'klines_1m_df', 'klines_15m_df', 'klines_1h_df', 'klines_4h_df',
#         'klines_1d_df', 'balance_df'
#     ], results))
#     return data_dfs
#
#
# def calculate_features(data_dfs):
#     """Calculates technical indicators and adds them to the dataframes."""
#     data_dfs['klines_1m_df'] = add_indicators(data_dfs['klines_1m_df'], INDICATORS_CONFIG)
#     data_dfs['klines_15m_df'] = add_indicators(data_dfs['klines_15m_df'], INDICATORS_CONFIG)
#     data_dfs['klines_1h_df'] = add_indicators(data_dfs['klines_1h_df'], INDICATORS_CONFIG)
#     return data_dfs
#
#
# def save_data_to_db(ticker: str, data_dfs):
#     """Saves the dataframes to the database."""
#     functions_with_kwargs = [
#         (insert_records, dict(data=[KLine(**item) for item in data_dfs['klines_1m_df'].to_dict(orient="records")],
#                               table_name="klines_1m",
#                               conflict_columns=("open_time", "close_time"),
#                               db_name=ticker)),
#         (insert_records, dict(data=[KLine(**item) for item in data_dfs['klines_15m_df'].to_dict(orient="records")],
#                               table_name="klines_15m",
#                               conflict_columns=("open_time", "close_time"),
#                               db_name=ticker)),
#         (insert_records, dict(data=[KLine(**item) for item in data_dfs['klines_1h_df'].to_dict(orient="records")],
#                               table_name="klines_1h",
#                               conflict_columns=("open_time", "close_time"),
#                               db_name=ticker)),
#         (insert_records, dict(data=[KLine(**item) for item in data_dfs['klines_4h_df'].to_dict(orient="records")],
#                               table_name="klines_4h",
#                               conflict_columns=("open_time", "close_time"),
#                               db_name=ticker)),
#         (insert_records, dict(data=[KLine(**item) for item in data_dfs['klines_1d_df'].to_dict(orient="records")],
#                               table_name="klines_1d",
#                               conflict_columns=("open_time", "close_time"),
#                               db_name=ticker))
#     ]
#     parallel_execute(functions_with_kwargs)
#
#
# def generate_images(ticker: str, data_dfs):
#     """Generates images for the LLM prompt."""
#     shutil.rmtree(IMAGES_PATH / ticker, ignore_errors=True)
#     functions_with_kwargs = [
#         (calc_swings_and_plot, dict(df=data_dfs['klines_1m_df'], ticker=ticker, interval="1m")),
#         (calc_swings_and_plot, dict(df=data_dfs['klines_15m_df'], ticker=ticker, interval="15m")),
#         (calc_swings_and_plot, dict(df=data_dfs['klines_1h_df'], ticker=ticker, interval="1h")),
#     ]
#     parallel_execute(functions_with_kwargs)
#     image_files = sorted((IMAGES_PATH / ticker).glob("*.png"), key=lambda x: os.path.getctime(x))
#     image_files = [img.as_posix() for img in image_files]
#     image_path = (IMAGES_PATH / ticker / "concated.png").as_posix()
#     concat_images(image_files, image_path)
#     return {"original_images": image_files, "concated_image": image_path}
#
#
# def prepare_trading_chart(ticker: str):
#     """
#     Collects data, calculates features, saves to DB, and generates images for trading charts.
#     """
#
#     asset = ticker[-4:]
#     if asset not in ("USDT", "USDC"):
#         raise ValueError(f"Wrong asset: {asset}. Asset must be USDT, USDC.")
#
#     if not is_db_exists(ticker):
#         apply_schema(ticker)
#
#     data_dfs = collect_data(ticker, asset)
#     data_dfs = calculate_features(data_dfs)
#     save_data_to_db(ticker, data_dfs)
#     image_path = generate_images(ticker, data_dfs)
#
#     return image_path
#
#
# if __name__ == "__main__":
#     image_path = prepare_trading_chart("ETHUSDT")
#     print(image_path)