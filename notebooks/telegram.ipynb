#%%
import os
import logging
from telegram import Update
from telegram.ext import Application, CommandHandler, ContextTypes
import asyncio
import subprocess
from PIL import Image
import io

from dotenv import load_dotenv
#%%
load_dotenv()
#%%
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)
#%%
TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
#%%
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Send a message when the command /start is issued."""
    await update.message.reply_text('Hi! I\'m your command execution bot. Use /help to see available commands.')

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Send a message when the command /help is issued."""
    help_text = """
    Available commands:
    /start - Start the bot
    /help - Show this help message
    /execute <command> - Execute a command on the backend
    /sendpic <filename> - Send a PNG image
    """
    await update.message.reply_text(help_text)

async def execute_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Execute a command on the backend and return the result."""
    if not context.args:
        await update.message.reply_text('Please provide a command to execute.')
        return

    command = ' '.join(context.args)
    try:
        result = await run_command(command)
        await update.message.reply_text(f"Command output:\n```\n{result}\n```", parse_mode='Markdown')
    except Exception as e:
        await update.message.reply_text(f"Error executing command: {str(e)}")

async def send_picture(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Send a PNG image."""
    if not context.args:
        await update.message.reply_text('Please provide an image filename.')
        return

    filename = context.args[0]
    try:
        with open(filename, 'rb') as image_file:
            await update.message.reply_photo(photo=image_file, caption=f"Image: {filename}")
    except FileNotFoundError:
        await update.message.reply_text(f"Image file not found: {filename}")
    except Exception as e:
        await update.message.reply_text(f"Error sending image: {str(e)}")

async def run_command(command: str) -> str:
    """Run a command on the backend and return the output."""
    try:
        process = await asyncio.create_subprocess_shell(
            command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        
        if process.returncode == 0:
            return stdout.decode().strip()
        else:
            return f"Command failed with error:\n{stderr.decode().strip()}"
    except Exception as e:
        return f"Error executing command: {str(e)}"
#%%
application = Application.builder().token(TOKEN).build()

# Add command handlers
application.add_handler(CommandHandler("start", start))
application.add_handler(CommandHandler("help", help_command))
application.add_handler(CommandHandler("execute", execute_command))
application.add_handler(CommandHandler("sendpic", send_picture))


#%%
# Run the bot until the user presses Ctrl-C
application.run_polling()
#%%
