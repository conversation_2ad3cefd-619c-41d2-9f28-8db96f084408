import os
import datetime
import pytest
import pandas as pd
from unittest.mock import patch, MagicMock

from src.utils.extract_data import (
    get_klines_df, get_orders_df, get_open_positions_df,
    get_position_history_df, get_current_price, get_balance_df
)


class TestExtractData:
    
    @pytest.fixture
    def mock_client(self):
        """Create a mock Binance client for testing."""
        with patch('src.utils.extract_data.get_client') as mock_get_client:
            mock_client = MagicMock()
            mock_get_client.return_value = mock_client
            yield mock_client
    
    @pytest.fixture
    def mock_klines_data(self):
        """Sample klines data for testing."""
        # Format: [open_time, open, high, low, close, volume, close_time, ...]
        now_ms = int(datetime.datetime.now().timestamp() * 1000)
        hour_ms = 60 * 60 * 1000  # 1 hour in milliseconds
        
        return [
            [now_ms - hour_ms * 2, "100", "110", "95", "105", "1000", now_ms - hour_ms, "10000", "50", "500", "5000", "0"],
            [now_ms - hour_ms, "105", "115", "100", "110", "1200", now_ms, "12000", "60", "600", "6000", "0"]
        ]
    
    @pytest.fixture
    def mock_orders_data(self):
        """Sample orders data for testing."""
        now_ms = int(datetime.datetime.now().timestamp() * 1000)
        
        return [
            {
                'orderId': 123456,
                'symbol': 'BTCUSDT',
                'status': 'FILLED',
                'clientOrderId': 'client123',
                'price': "50000",
                'avgPrice': "50100",
                'origQty': "0.1",
                'executedQty': "0.1",
                'cumQuote': "5010",
                'timeInForce': 'GTC',
                'type': 'LIMIT',
                'reduceOnly': False,
                'closePosition': False,
                'side': 'BUY',
                'positionSide': 'LONG',
                'stopPrice': "0",
                'workingType': 'CONTRACT_PRICE',
                'priceProtect': False,
                'origType': 'LIMIT',
                'priceMatch': 'NONE',
                'selfTradePreventionMode': 'NONE',
                'goodTillDate': 0,
                'time': now_ms - 3600000,
                'updateTime': now_ms
            }
        ]
    
    @pytest.fixture
    def mock_positions_data(self):
        """Sample positions data for testing."""
        now_ms = int(datetime.datetime.now().timestamp() * 1000)
        
        return [
            {
                'symbol': 'BTCUSDT',
                'positionAmt': '0.1',
                'entryPrice': '50000',
                'breakEvenPrice': '50100',
                'markPrice': '51000',
                'unRealizedProfit': '100',
                'liquidationPrice': '45000',
                'leverage': '10',
                'maxNotionalValue': '100000',
                'marginType': 'isolated',
                'isolatedMargin': '500',
                'isAutoAddMargin': False,
                'positionSide': 'LONG',
                'notional': '5100',
                'isolatedWallet': '600',
                'updateTime': now_ms,
                'isolated': True,
                'adlQuantile': '1'
            },
            {
                'symbol': 'ETHUSDT',
                'positionAmt': '0',  # Zero position should be filtered out
                'entryPrice': '3000',
                'breakEvenPrice': '3010',
                'markPrice': '3100',
                'unRealizedProfit': '0',
                'liquidationPrice': '2800',
                'leverage': '10',
                'maxNotionalValue': '50000',
                'marginType': 'isolated',
                'isolatedMargin': '300',
                'isAutoAddMargin': False,
                'positionSide': 'LONG',
                'notional': '0',
                'isolatedWallet': '300',
                'updateTime': now_ms,
                'isolated': True,
                'adlQuantile': '1'
            }
        ]
    
    @pytest.fixture
    def mock_trades_data(self):
        """Sample trade history data for testing."""
        now_ms = int(datetime.datetime.now().timestamp() * 1000)
        hour_ms = 3600000
        
        return [
            # Buy order
            {
                'symbol': 'BTCUSDT',
                'id': 1,
                'order_id': 100,
                'side': 'BUY',
                'price': '50000',
                'qty': '0.1',
                'realizedPnl': '0',
                'quoteQty': '5000',
                'commission': '1',
                'commissionAsset': 'USDT',
                'time': now_ms - hour_ms * 2,
                'positionSide': 'LONG',
                'maker': True,
                'buyer': True
            },
            # Sell order that matches the buy
            {
                'symbol': 'BTCUSDT',
                'id': 2,
                'order_id': 101,
                'side': 'SELL',
                'price': '51000',
                'qty': '0.1',
                'realizedPnl': '100',
                'quoteQty': '5100',
                'commission': '1',
                'commissionAsset': 'USDT',
                'time': now_ms - hour_ms,
                'positionSide': 'LONG',
                'maker': False,
                'buyer': False
            }
        ]
    
    @pytest.fixture
    def mock_ticker_data(self):
        """Sample ticker price data for testing."""
        now_ms = int(datetime.datetime.now().timestamp() * 1000)
        
        return {
            'symbol': 'BTCUSDT',
            'price': '51000',
            'time': now_ms
        }
    
    @pytest.fixture
    def mock_balance_data(self):
        """Sample balance data for testing."""
        now_ms = int(datetime.datetime.now().timestamp() * 1000)
        
        return [
            {
                'accountAlias': 'primary',
                'asset': 'USDT',
                'balance': '10000',
                'crossWalletBalance': '9500',
                'crossUnPnl': '100',
                'availableBalance': '9000',
                'maxWithdrawAmount': '9000',
                'marginAvailable': True,
                'updateTime': now_ms
            },
            {
                'accountAlias': 'primary',
                'asset': 'BTC',
                'balance': '0.5',
                'crossWalletBalance': '0.5',
                'crossUnPnl': '0.01',
                'availableBalance': '0.49',
                'maxWithdrawAmount': '0.49',
                'marginAvailable': True,
                'updateTime': now_ms
            }
        ]

    def test_get_klines_df(self, mock_client, mock_klines_data):
        """Test fetching klines data."""
        # Setup mock
        mock_client.klines.return_value = mock_klines_data
        
        # Test with default parameters
        symbol = "BTCUSDT"
        interval = "1h"
        limit = 2
        
        df = get_klines_df(symbol, interval, limit)
        
        # Verify mock was called correctly
        mock_client.klines.assert_called_once()
        call_args = mock_client.klines.call_args[1]
        assert call_args['symbol'] == symbol
        assert call_args['interval'] == interval
        assert call_args['limit'] == limit
        
        # Verify output dataframe
        assert not df.empty
        assert len(df) == 2
        assert 'open_price' in df.columns
        assert 'close_price' in df.columns
        assert 'open_time' in df.columns
        assert isinstance(df['open_time'].iloc[0], pd.Timestamp)
        assert df['number_of_trades'].dtype == int
        
    def test_get_klines_df_with_end_time(self, mock_client, mock_klines_data):
        """Test fetching klines data with end_time parameter."""
        # Setup mock
        mock_client.klines.return_value = mock_klines_data
        
        # Test with end_time
        end_time = datetime.datetime.now().isoformat()
        
        df = get_klines_df("BTCUSDT", "1h", 2, end_time=end_time)
        
        # Verify end_time was properly converted and passed to the API
        mock_client.klines.assert_called_once()
        assert 'endTime' in mock_client.klines.call_args[1]
        
    def test_get_orders_df(self, mock_client, mock_orders_data):
        """Test fetching orders data."""
        # Setup mock
        mock_client.get_orders.return_value = mock_orders_data
        
        # Test with symbol parameter
        symbol = "BTCUSDT"
        df = get_orders_df(symbol)
        
        # Verify mock was called correctly
        mock_client.get_orders.assert_called_once_with(symbol=symbol)
        
        # Verify output dataframe
        assert not df.empty
        assert len(df) == 1
        assert 'order_id' in df.columns
        assert 'price' in df.columns
        assert isinstance(df['time'].iloc[0], datetime.datetime)
        
    def test_get_orders_df_with_drop_columns(self, mock_client, mock_orders_data):
        """Test fetching orders data with columns to drop."""
        # Setup mock
        mock_client.get_orders.return_value = mock_orders_data
        
        # Test with columns to drop
        df = get_orders_df(drop_columns=['price', 'stop_price'])
        
        # Verify columns were dropped
        assert 'price' not in df.columns
        assert 'stop_price' not in df.columns
        
    def test_get_open_positions_df(self, mock_client, mock_positions_data):
        """Test fetching open positions data."""
        # Setup mock
        mock_client.get_position_risk.return_value = mock_positions_data
        
        # Test with default parameters
        df = get_open_positions_df()
        
        # Verify mock was called correctly
        mock_client.get_position_risk.assert_called_once_with()
        
        # Verify output dataframe
        assert not df.empty
        assert 'symbol' in df.columns
        
    def test_get_open_positions_df_with_symbol(self, mock_client, mock_positions_data):
        """Test fetching open positions data for a specific symbol."""
        # Setup mock
        mock_client.get_position_risk.return_value = mock_positions_data
        
        # Test with symbol parameter
        symbol = "BTCUSDT"
        df = get_open_positions_df(symbol)
        
        # Verify mock was called correctly
        mock_client.get_position_risk.assert_called_once_with(symbol=symbol)
        
    def test_get_position_history_df(self, mock_client, mock_trades_data):
        """Test fetching position history data."""
        # Setup mock
        mock_client.get_account_trades.return_value = mock_trades_data
        
        # Test with default parameters
        df = get_position_history_df()
        
        # Verify mock was called correctly
        mock_client.get_account_trades.assert_called_once_with()
        
        # Verify output dataframe
        assert not df.empty
        assert len(df) == 1
        assert 'symbol' in df.columns
        assert 'quantity' in df.columns
        assert 'entry_price' in df.columns
        assert 'exit_price' in df.columns
        assert 'pnl' in df.columns
        assert isinstance(df['position_open_time'].iloc[0], datetime.datetime)
        
    def test_get_current_price(self, mock_client, mock_ticker_data):
        """Test fetching current price data."""
        # Setup mock
        mock_client.ticker_price.return_value = mock_ticker_data
        
        # Test with symbol parameter
        symbol = "BTCUSDT"
        ticker = get_current_price(symbol)
        
        # Verify mock was called correctly
        mock_client.ticker_price.assert_called_once_with(symbol=symbol)
        
        # Verify output
        assert ticker['symbol'] == symbol
        assert ticker['price'] == 51000.0
        assert isinstance(ticker['price'], float)
        
    def test_get_balance_df(self, mock_client, mock_balance_data):
        """Test fetching balance data."""
        # Setup mock
        mock_client.balance.return_value = mock_balance_data
        
        # Test with asset parameter
        asset = "USDT"
        df = get_balance_df(asset)
        
        # Verify mock was called correctly
        mock_client.balance.assert_called_once()
        
        # Verify output dataframe
        assert not df.empty
        assert len(df) == 1
        assert 'asset' in df.columns
        assert 'balance' in df.columns
        assert 'available_balance' in df.columns
        assert df['asset'].iloc[0] == asset
        # 'update_time' is dropped by default in get_balance_df
