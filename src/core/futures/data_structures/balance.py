import datetime
import pandas as pd

from dataclasses import dataclass, field, asdict
from typing import Any, Optional


@dataclass
class Balance:
    asset: str
    balance: float
    available_balance: float

    account_alias: Optional[str] = None
    cross_wallet_balance: Optional[float] = None
    cross_un_pnl: Optional[float] = None
    max_withdraw_amount: Optional[float] = None
    margin_available: Optional[bool] = None
    
    update_time: Any = None  # Can handle datetime, pd.Timestamp, or ISO format strings

    def __post_init__(self):
        if isinstance(self.update_time, pd.Timestamp):
            self.update_time = self.update_time.to_pydatetime()
        elif isinstance(self.update_time, str):
            self.update_time = datetime.datetime.fromisoformat(self.update_time)

    def dict(self):
        data = asdict(self)
        # Remove keys with None values
        data = {k: v for k, v in data.items() if v is not None}
        if 'update_time' in data:
            data['update_time'] = self.update_time.strftime('%Y-%m-%d %H:%M:%S')
        for key in ['balance', 'cross_wallet_balance', 'cross_un_pnl', 'available_balance', 'max_withdraw_amount']:
            if key in data:
                data[key] = round(data[key], 4)
        return data
