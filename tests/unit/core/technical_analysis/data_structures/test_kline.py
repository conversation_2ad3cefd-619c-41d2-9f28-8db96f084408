import pytest
import datetime
import pandas as pd

from src.core.technical_analysis.data_structures.kline import KLine


class TestKLine:
    """Test cases for the KLine dataclass."""

    def test_initialization_with_numeric_values(self):
        """Test KLine initialization with numeric values."""
        kline = KLine(
            open_time=1638360000000,  # 2021-12-01 13:20:00
            open_price=50000.0,
            high_price=51000.0,
            low_price=49500.0,
            close_price=50500.0,
            volume=10.5,
            close_time=1638360059999,  # 2021-12-01 13:20:59
            quote_asset_volume=525000.0,
            number_of_trades=100,
            taker_buy_base_asset_volume=6.3,
            taker_buy_quote_asset_volume=315000.0
        )

        # Check time conversions
        assert isinstance(kline.open_time, datetime.datetime)
        assert isinstance(kline.close_time, datetime.datetime)

        # Check numeric conversions
        assert kline.open_price == 50000.0
        assert kline.high_price == 51000.0
        assert kline.low_price == 49500.0
        assert kline.close_price == 50500.0
        assert kline.volume == 10.5

        # Check calculated fields
        assert kline.status == "green"  # Since close_price > open_price
        assert kline.buy_volume_ratio == 0.6  # 6.3 / 10.5
        assert kline.sell_volume_ratio == 0.4  # 1 - 0.6

    def test_initialization_with_string_values(self):
        """Test KLine initialization with string values."""
        kline = KLine(
            open_time="2021-12-01T13:20:00",
            open_price="50000",
            high_price="51000",
            low_price="49500",
            close_price="50500",
            volume="10.5",
            close_time="2021-12-01T13:20:59",
            quote_asset_volume="525000",
            number_of_trades="100",
            taker_buy_base_asset_volume="6.3",
            taker_buy_quote_asset_volume="315000"
        )

        # Check time conversions
        assert isinstance(kline.open_time, datetime.datetime)
        assert isinstance(kline.close_time, datetime.datetime)

        # Check numeric conversions
        assert kline.open_price == 50000.0
        assert kline.high_price == 51000.0
        assert kline.low_price == 49500.0
        assert kline.close_price == 50500.0
        assert kline.volume == 10.5
        assert kline.number_of_trades == 100

        # Check calculated fields
        assert kline.status == "green"
        assert kline.buy_volume_ratio == 0.6
        assert kline.sell_volume_ratio == 0.4

    def test_initialization_with_pandas_timestamp(self):
        """Test KLine initialization with pandas Timestamp objects."""
        kline = KLine(
            open_time=pd.Timestamp("2021-12-01 13:20:00"),
            open_price=50000.0,
            high_price=51000.0,
            low_price=49500.0,
            close_price=50500.0,
            volume=10.5,
            close_time=pd.Timestamp("2021-12-01 13:20:59"),
            quote_asset_volume=525000.0,
            number_of_trades=100,
            taker_buy_base_asset_volume=6.3,
            taker_buy_quote_asset_volume=315000.0
        )

        # Check time conversions
        assert isinstance(kline.open_time, datetime.datetime)
        assert isinstance(kline.close_time, datetime.datetime)
        assert kline.open_time.year == 2021
        assert kline.open_time.month == 12
        assert kline.open_time.day == 1
        assert kline.open_time.hour == 13
        assert kline.open_time.minute == 20

    def test_red_candle_status(self):
        """Test status calculation for a red candle (close < open)."""
        kline = KLine(
            open_time=1638360000000,
            open_price=50000.0,
            high_price=51000.0,
            low_price=49000.0,
            close_price=49500.0,  # Lower than open_price
            volume=10.5,
            close_time=1638360059999,
            taker_buy_base_asset_volume=5.0  # Added missing parameter
        )

        assert kline.status == "red"

    def test_status_override(self):
        """Test that providing an incorrect status gets corrected."""
        kline = KLine(
            open_time=1638360000000,
            open_price=50000.0,
            high_price=51000.0,
            low_price=49000.0,
            close_price=51000.0,  # Green candle
            volume=10.5,
            close_time=1638360059999,
            status="red",  # Incorrect status provided
            taker_buy_base_asset_volume=5.0  # Added missing parameter
        )

        # Status should be corrected to green
        assert kline.status == "green"

    def test_zero_volume_handling(self):
        """Test handling of zero volume cases."""
        kline = KLine(
            open_time=1638360000000,
            open_price=50000.0,
            high_price=50000.0,
            low_price=50000.0,
            close_price=50000.0,
            volume=0,  # Zero volume
            close_time=1638360059999,
            taker_buy_base_asset_volume=0
        )

        # Volume ratios should be set to 0
        assert kline.buy_volume_ratio == 0
        assert kline.sell_volume_ratio == 0

    def test_dict_method(self):
        """Test the dict method for serialization."""
        kline = KLine(
            open_time=1638360000000,
            open_price=50000.0,
            high_price=51000.0,
            low_price=49500.0,
            close_price=50500.0,
            volume=10.5,
            close_time=1638360059999,
            quote_asset_volume=525000.0,
            number_of_trades=100,
            taker_buy_base_asset_volume=6.3,
            taker_buy_quote_asset_volume=315000.0,
            # Add some indicators
            sma_7=50200.0,
            rsi=65.5
        )

        result = kline.dict()

        # Check datetime formatting
        assert isinstance(result["open_time"], str)
        assert isinstance(result["close_time"], str)

        # Check numeric rounding
        assert result["rsi"] == 65.5
        assert result["open_price"] == 50000.0

        # Check that all expected fields are present
        expected_fields = [
            "open_time", "close_time", "open_price", "high_price", "low_price",
            "close_price", "volume", "quote_asset_volume", "number_of_trades",
            "taker_buy_base_asset_volume", "taker_buy_quote_asset_volume",
            "status", "buy_volume_ratio", "sell_volume_ratio", "sma_7", "rsi"
        ]
        for field in expected_fields:
            assert field in result

        # Fields with None values should be excluded
        assert "sma_25" not in result
        assert "ema_7" not in result

    def test_invalid_time_format(self):
        """Test handling of invalid time formats."""
        with pytest.raises(ValueError, match="Unsupported time format"):
            KLine(
                open_time={"invalid": "format"},  # Invalid format
                open_price=50000.0,
                high_price=51000.0,
                low_price=49500.0,
                close_price=50500.0,
                volume=10.5,
                close_time=1638360059999,
                taker_buy_base_asset_volume=5.0  # Added missing parameter
            )

    def test_different_timestamp_formats(self):
        """Test different timestamp formats for initialization."""
        # Unix timestamp in seconds
        kline1 = KLine(
            open_time=1638360000,  # seconds
            open_price=50000.0,
            high_price=51000.0,
            low_price=49500.0,
            close_price=50500.0,
            volume=10.5,
            close_time=1638360059,
            taker_buy_base_asset_volume=5.0  # Added missing parameter
        )

        # Unix timestamp in milliseconds
        kline2 = KLine(
            open_time=1638360000000,  # milliseconds
            open_price=50000.0,
            high_price=51000.0,
            low_price=49500.0,
            close_price=50500.0,
            volume=10.5,
            close_time=1638360059000,
            taker_buy_base_asset_volume=5.0  # Added missing parameter
        )

        # Both should be converted to datetime objects
        assert isinstance(kline1.open_time, datetime.datetime)
        assert isinstance(kline2.open_time, datetime.datetime)

        # The resulting datetime objects should be close
        assert abs((kline1.open_time - kline2.open_time).total_seconds()) < 1

    def test_indicators_initialization(self):
        """Test initialization with technical indicators."""
        kline = KLine(
            open_time=1638360000000,
            open_price=50000.0,
            high_price=51000.0,
            low_price=49500.0,
            close_price=50500.0,
            volume=10.5,
            close_time=1638360059999,
            sma_7=50200.0,
            sma_25=50100.0,
            ema_7=50250.0,
            rsi=65.5,
            macd=100.0,
            macd_signal=90.0,
            macd_hist=10.0,
            bb_upper=51500.0,
            bb_middle=50000.0,
            bb_lower=48500.0,
            taker_buy_base_asset_volume=6.0  # Added missing parameter
        )

        # Check indicator values
        assert kline.sma_7 == 50200.0
        assert kline.sma_25 == 50100.0
        assert kline.ema_7 == 50250.0
        assert kline.rsi == 65.5
        assert kline.macd == 100.0
        assert kline.macd_signal == 90.0
        assert kline.macd_hist == 10.0
        assert kline.bb_upper == 51500.0
        assert kline.bb_middle == 50000.0
        assert kline.bb_lower == 48500.0