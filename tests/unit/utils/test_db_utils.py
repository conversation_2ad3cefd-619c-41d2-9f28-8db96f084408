import os
import sqlite3
import unittest
from dataclasses import dataclass
from pathlib import Path
from unittest import mock

import pytest

from src.utils.db_utils import (
    create_db_connector,
    apply_schema, 
    is_db_exists, 
    read_sql_from_file, 
    escape_column_name, 
    insert_records, 
    delete_records, 
    fetch_last_n_records,
    count_records
)


@dataclass
class TestRecord:
    id: int
    name: str
    value: float


class TestDBUtils(unittest.TestCase):
    """Test class for database utility functions."""
    
    def setUp(self):
        """Set up test environment before each test."""
        self.test_db_name = "test_db"
        self.test_db_path = Path("test_sqlite") / "sqlite" / f"{self.test_db_name}.db"
        
        # Create a mock for DATA_PATH
        self.data_path_patcher = mock.patch("src.utils.db_utils.DATA_PATH", Path("test_sqlite"))
        self.mock_data_path = self.data_path_patcher.start()
        
        # Create test directories with proper structure
        os.makedirs(Path("test_sqlite") / "sqlite", exist_ok=True)
        
    def tearDown(self):
        """Clean up after each test."""
        # Stop patchers
        self.data_path_patcher.stop()
        
        # Remove test database if it exists
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
            
        # Remove test directories in the correct order (from deepest to shallowest)
        sqlite_dir = Path("test_sqlite") / "sqlite"
        if os.path.exists(sqlite_dir):
            os.rmdir(sqlite_dir)
            
        if os.path.exists("test_sqlite"):
            os.rmdir("test_sqlite")
            
    def test_create_db_connector(self):
        """Test creating a database connection."""
        # Test with valid db_name
        conn = create_db_connector(self.test_db_name)
        self.assertIsInstance(conn, sqlite3.Connection)
        conn.close()
        
        # Test with None db_name should raise ValueError
        with self.assertRaises(ValueError):
            create_db_connector(None)
            
    def test_is_db_exists(self):
        """Test checking if a database exists."""
        # Database should not exist initially
        self.assertFalse(is_db_exists(self.test_db_name))
        
        # Create the database
        conn = create_db_connector(self.test_db_name)
        conn.close()
        
        # Now database should exist
        self.assertTrue(is_db_exists(self.test_db_name))
        
    def test_read_sql_from_file(self):
        """Test reading SQL from a file."""
        # Create a temp SQL file
        test_sql_content = "CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT);"
        test_sql_path = "test_schema.sql"
        
        with open(test_sql_path, "w") as f:
            f.write(test_sql_content)
        
        try:
            # Test reading the SQL file
            sql = read_sql_from_file(test_sql_path)
            self.assertEqual(sql, test_sql_content)
        finally:
            # Clean up
            os.remove(test_sql_path)
            
    @mock.patch("src.utils.db_utils.read_sql_from_file")
    @mock.patch("src.utils.db_utils.DB_SCHEMA_PATH")
    def test_apply_schema(self, mock_db_schema_path, mock_read_sql):
        """Test applying a schema to a database."""
        # Set up mocks
        mock_db_schema_path.as_posix.return_value = "mock_path"
        mock_read_sql.return_value = "CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT);"
        
        # Apply schema
        apply_schema(self.test_db_name)
        
        # Verify the schema was applied
        conn = create_db_connector(self.test_db_name)
        cursor = conn.cursor()
        
        # Check if the table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test'")
        tables = cursor.fetchall()
        
        self.assertEqual(len(tables), 1)
        self.assertEqual(tables[0][0], "test")
        conn.close()
        
    def test_escape_column_name(self):
        """Test escaping column names."""
        self.assertEqual(escape_column_name("test"), '"test"')
        self.assertEqual(escape_column_name("column with space"), '"column with space"')
        self.assertEqual(escape_column_name("table.column"), '"table.column"')
        
    def test_insert_records(self):
        """Test inserting records into a table."""
        # Create a test table
        conn = create_db_connector(self.test_db_name)
        cursor = conn.cursor()
        cursor.execute("CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT, value REAL)")
        conn.commit()
        conn.close()
        
        # Create test records
        records = [
            TestRecord(id=1, name="test1", value=1.1),
            TestRecord(id=2, name="test2", value=2.2),
            TestRecord(id=3, name="test3", value=3.3)
        ]
        
        # Insert records
        insert_records(records, "test", db_name=self.test_db_name)
        
        # Verify records were inserted
        conn = create_db_connector(self.test_db_name)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM test ORDER BY id")
        result = cursor.fetchall()
        conn.close()
        
        self.assertEqual(len(result), 3)
        self.assertEqual(result[0], (1, "test1", 1.1))
        self.assertEqual(result[1], (2, "test2", 2.2))
        self.assertEqual(result[2], (3, "test3", 3.3))
        
    def test_insert_records_with_conflict(self):
        """Test inserting records with conflict resolution."""
        # Create a test table
        conn = create_db_connector(self.test_db_name)
        cursor = conn.cursor()
        cursor.execute("CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT, value REAL)")
        conn.commit()
        conn.close()
        
        # Create initial records
        records = [
            TestRecord(id=1, name="test1", value=1.1),
            TestRecord(id=2, name="test2", value=2.2)
        ]
        
        # Insert initial records
        insert_records(records, "test", db_name=self.test_db_name)
        
        # Create records with conflicts
        updated_records = [
            TestRecord(id=1, name="updated1", value=1.5),
            TestRecord(id=3, name="test3", value=3.3)
        ]
        
        # Insert records with conflict resolution
        insert_records(updated_records, "test", conflict_columns=["id"], db_name=self.test_db_name)
        
        # Verify records were correctly inserted/updated
        conn = create_db_connector(self.test_db_name)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM test ORDER BY id")
        result = cursor.fetchall()
        conn.close()
        
        self.assertEqual(len(result), 3)
        self.assertEqual(result[0], (1, "updated1", 1.5))  # Updated record
        self.assertEqual(result[1], (2, "test2", 2.2))     # Unchanged record
        self.assertEqual(result[2], (3, "test3", 3.3))     # New record
        
    def test_delete_records(self):
        """Test deleting records from a table."""
        # Create a test table and insert records
        conn = create_db_connector(self.test_db_name)
        cursor = conn.cursor()
        cursor.execute("CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT, value REAL)")
        cursor.executemany("INSERT INTO test VALUES (?, ?, ?)", 
                          [(1, "test1", 1.1), 
                           (2, "test2", 2.2), 
                           (3, "test3", 3.3)])
        conn.commit()
        conn.close()
        
        # Delete a specific record
        delete_records("test", "id = ?", (2,), db_name=self.test_db_name)
        
        # Verify the record was deleted
        conn = create_db_connector(self.test_db_name)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM test ORDER BY id")
        result = cursor.fetchall()
        conn.close()
        
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0], (1, "test1", 1.1))
        self.assertEqual(result[1], (3, "test3", 3.3))
        
        # Delete all records
        delete_records("test", db_name=self.test_db_name)
        
        # Verify all records were deleted
        conn = create_db_connector(self.test_db_name)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM test")
        result = cursor.fetchall()
        conn.close()
        
        self.assertEqual(len(result), 0)
        
    def test_fetch_last_n_records(self):
        """Test fetching the last N records from a table."""
        # Create a test table and insert records
        conn = create_db_connector(self.test_db_name)
        cursor = conn.cursor()
        cursor.execute("CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT, value REAL)")
        cursor.executemany("INSERT INTO test VALUES (?, ?, ?)", 
                          [(1, "test1", 1.1), 
                           (2, "test2", 2.2), 
                           (3, "test3", 3.3), 
                           (4, "test4", 4.4), 
                           (5, "test5", 5.5)])
        conn.commit()
        conn.close()
        
        # Fetch the last 3 records
        result = fetch_last_n_records("test", 3, db_name=self.test_db_name)
        
        self.assertEqual(len(result), 3)
        self.assertEqual(result[0], (5, "test5", 5.5))
        self.assertEqual(result[1], (4, "test4", 4.4))
        self.assertEqual(result[2], (3, "test3", 3.3))
        
    def test_count_records(self):
        """Test counting records in a table."""
        # Create a test table and insert records
        conn = create_db_connector(self.test_db_name)
        cursor = conn.cursor()
        cursor.execute("CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT, value REAL)")
        cursor.executemany("INSERT INTO test VALUES (?, ?, ?)", 
                          [(1, "test1", 1.1), 
                           (2, "test2", 2.2), 
                           (3, "test3", 3.3)])
        conn.commit()
        conn.close()
        
        # Count records
        count = count_records("test", db_name=self.test_db_name)
        
        self.assertEqual(count, 3)


if __name__ == "__main__":
    unittest.main()
