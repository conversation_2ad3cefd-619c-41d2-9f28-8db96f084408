import os
import pytest
import pandas as pd
import sqlite3
from pathlib import Path

from tasks.prepare_trading_chart import prepare_trading_chart
from src.utils.db_utils import is_db_exists
from src.config.base import IMAGES_PATH


class TestPrepareChartIntegration:
    """Integration tests for prepare_trading_chart function with real data."""
    
    # Test parameters
    ticker = "ETHUSDT"
    asset = "ETH"
    
    def test_prepare_trading_chart_full_pipeline(self):
        """Test the entire pipeline of prepare_trading_chart with real data."""
        # Execute the function
        image_path = prepare_trading_chart(self.ticker, self.asset)
        
        # Assert the function returned a valid path
        assert image_path is not None
        assert isinstance(image_path, Path)
        assert image_path.exists()
        assert str(image_path).endswith(f"{self.ticker}/concated.png")
        
        # Verify that individual chart images were also created
        chart_dir = IMAGES_PATH / self.ticker
        assert chart_dir.exists()
        chart_files = list(chart_dir.glob("*.png"))
        # Should have at least 3 charts (1m, 15m, 1h) plus the concatenated one
        assert len(chart_files) >= 4
        
        # Verify that database was created and contains data
        assert is_db_exists(self.ticker)
        
        # Connect to the database and check if tables have data
        conn = sqlite3.connect(f"data/sqlite/{self.ticker}.db")
        
        # Check tables and count records
        tables = ["klines_1m", "klines_15m", "klines_1h", "klines_4h", "klines_1d"]
        for table in tables:
            df = pd.read_sql(f"SELECT COUNT(*) as count FROM {table}", conn)
            count = df["count"].iloc[0]
            assert count > 0, f"Table {table} should contain records"
            
            # Verify that indicators were calculated (spot check a few columns)
            columns = pd.read_sql(f"PRAGMA table_info({table})", conn)["name"].tolist()
            indicator_columns = ["rsi", "macd", "macd_signal", "ema_7", "ema_25"]
            for indicator in indicator_columns:
                assert indicator in columns, f"Indicator {indicator} should be in {table}"
            
            # Check for a few specific records with technical indicators
            if table in ["klines_1m", "klines_15m", "klines_1h"]:
                indicator_df = pd.read_sql(f"SELECT * FROM {table} WHERE rsi IS NOT NULL LIMIT 5", conn)
                assert not indicator_df.empty, f"Table {table} should have rows with calculated indicators"
        
        conn.close()
    
    def test_incremental_update(self):
        """Test that the function works correctly when run a second time (incremental update)."""
        # Run the function once
        first_run_image = prepare_trading_chart(self.ticker, self.asset)
        assert first_run_image.exists()
        
        # Get the file modification time of the database
        db_path = Path(f"data/sqlite/{self.ticker}.db")
        first_mtime = os.path.getmtime(db_path)
        
        # Run the function again
        second_run_image = prepare_trading_chart(self.ticker, self.asset)
        assert second_run_image.exists()
        
        # Get the new modification time
        second_mtime = os.path.getmtime(db_path)
        
        # DB should have been modified in the second run
        assert second_mtime >= first_mtime
        
        # Both runs should produce a valid image
        assert str(first_run_image) == str(second_run_image)
