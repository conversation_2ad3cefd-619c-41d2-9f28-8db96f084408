import pytest
from unittest.mock import patch, MagicMock
import datetime

from src.binance_client import get_client, get_server_time


# Fixtures
@pytest.fixture
def mock_binance_keys():
    """Patch the Binance API keys for testing."""
    with patch('src.binance_client.BINANCE_API_KEY', 'test_key'), \
            patch('src.binance_client.BINANCE_SECRET_KEY', 'test_secret'):
        yield


@pytest.fixture
def mock_spot_client():
    """Mock the Spot client."""
    with patch('src.binance_client.Spot') as mock_spot:
        mock_instance = MagicMock()
        mock_spot.return_value = mock_instance
        yield mock_spot, mock_instance


@pytest.fixture
def mock_cm_futures_client():
    """Mock the CM Futures client."""
    with patch('src.binance_client.CMFutures') as mock_cm:
        mock_instance = MagicMock()
        mock_cm.return_value = mock_instance
        yield mock_cm, mock_instance


@pytest.fixture
def mock_um_futures_client():
    """Mock the UM Futures client."""
    with patch('src.binance_client.UMFutures') as mock_um:
        mock_instance = MagicMock()
        mock_um.return_value = mock_instance
        yield mock_um, mock_instance


# Tests for get_client
def test_get_spot_client(mock_binance_keys, mock_spot_client):
    """Test getting a Spot client."""
    mock_spot, mock_instance = mock_spot_client

    client = get_client("spot")

    mock_spot.assert_called_once_with(api_key='test_key', api_secret='test_secret')
    assert client == mock_instance


def test_get_cm_futures_client(mock_binance_keys, mock_cm_futures_client):
    """Test getting a CM Futures client."""
    mock_cm, mock_instance = mock_cm_futures_client

    client = get_client("cm_futures")

    mock_cm.assert_called_once_with(key='test_key', secret='test_secret')
    assert client == mock_instance


def test_get_um_futures_client(mock_binance_keys, mock_um_futures_client):
    """Test getting a UM Futures client."""
    mock_um, mock_instance = mock_um_futures_client

    client = get_client("um_futures")

    mock_um.assert_called_once_with(key='test_key', secret='test_secret')
    assert client == mock_instance


def test_get_client_invalid_source(mock_binance_keys):
    """Test getting a client with an invalid source."""
    with pytest.raises(ValueError) as exc_info:
        get_client("invalid_source")

    assert "Invalid source" in str(exc_info.value)


def test_get_client_missing_api_key():
    """Test getting a client with missing API key."""
    with patch('src.binance_client.BINANCE_API_KEY', None), \
            patch('src.binance_client.BINANCE_SECRET_KEY', 'test_secret'):
        with pytest.raises(ValueError) as exc_info:
            get_client("spot")

        assert "API Key/Secret not configured" in str(exc_info.value)


# Tests for get_server_time
def test_get_server_time_string():
    """Test getting server time as a string."""
    with patch('src.binance_client.get_client') as mock_get_client:
        mock_client = MagicMock()
        mock_client.time.return_value = {'serverTime': 1617979441000}  # Example timestamp
        mock_get_client.return_value = mock_client

        result = get_server_time(to_str=True, client_name="um_futures")

        mock_get_client.assert_called_once_with("um_futures")
        mock_client.time.assert_called_once()

        # This equals 2021-04-09 12:44:01
        expected_time = datetime.datetime.fromtimestamp(1617979441).strftime('%Y-%m-%d %H:%M:%S')
        assert result == expected_time


def test_get_server_time_datetime():
    """Test getting server time as a datetime object."""
    with patch('src.binance_client.get_client') as mock_get_client:
        mock_client = MagicMock()
        mock_client.time.return_value = {'serverTime': 1617979441000}  # Example timestamp
        mock_get_client.return_value = mock_client

        result = get_server_time(to_str=False, client_name="spot")

        mock_get_client.assert_called_once_with("spot")
        mock_client.time.assert_called_once()

        expected_dt = datetime.datetime.fromtimestamp(1617979441)
        assert result == expected_dt


def test_get_server_time_keyerror():
    """Test handling of KeyError when getting server time."""
    with patch('src.binance_client.get_client') as mock_get_client:
        mock_client = MagicMock()
        mock_client.time.side_effect = KeyError("serverTime not found")
        mock_get_client.return_value = mock_client

        with pytest.raises(ValueError) as exc_info:
            get_server_time(client_name="cm_futures")

        assert "Failed to initialize client" in str(exc_info.value)


def test_get_server_time_general_exception():
    """Test handling of general exception when getting server time."""
    with patch('src.binance_client.get_client') as mock_get_client:
        mock_client = MagicMock()
        mock_client.time.side_effect = Exception("Network error")
        mock_get_client.return_value = mock_client

        with pytest.raises(Exception) as exc_info:
            get_server_time(client_name="um_futures")

        assert str(exc_info.value) == "Network error"