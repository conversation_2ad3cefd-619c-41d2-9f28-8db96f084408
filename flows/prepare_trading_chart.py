# import os
# import json
# import shutil
# import datetime
#
# from prefect import flow, task
# from confluent_kafka import Producer
#
# from src.core.technical_analysis.indicators import add_indicators
# from src.core.technical_analysis.data_structures import KLine
# from src.utils.extract_data import get_klines_df
# from src.utils.image_utils import concat_images, upload_to_minio
# from src.utils.db_utils import insert_records, is_db_exists, apply_schema
# from src.utils.misc import parallel_execute, calc_swings_and_plot
# from src.config.logs import setup_prefect_logging
# from src.config.base import KAFKA_SERVERS, KAFKA_TOPIC, IMAGES_PATH, INTERVAL_LIMIT_MAPPING, INDICATORS_CONFIG
#
#
# # @task
# def collect_data_task(ticker: str):
#     functions_with_kwargs = [
#         (get_klines_df, dict(symbol=ticker, interval="1m", limit=INTERVAL_LIMIT_MAPPING["1m"] + 1)),
#         (get_klines_df, dict(symbol=ticker, interval="15m", limit=INTERVAL_LIMIT_MAPPING["15m"] + 1)),
#         (get_klines_df, dict(symbol=ticker, interval="1h", limit=INTERVAL_LIMIT_MAPPING["1h"] + 1)),
#         (get_klines_df, dict(symbol=ticker, interval="4h", limit=INTERVAL_LIMIT_MAPPING["4h"] + 1)),
#         (get_klines_df, dict(symbol=ticker, interval="1d", limit=INTERVAL_LIMIT_MAPPING["1d"] + 1))
#     ]
#     results = parallel_execute(functions_with_kwargs)
#     data_dfs = dict(zip([
#         'klines_1m_df', 'klines_15m_df', 'klines_1h_df', 'klines_4h_df',
#         'klines_1d_df'
#     ], results))
#     return data_dfs
#
#
# # @task
# def calculate_features_task(data_dfs):
#     """Calculates technical indicators and adds them to the dataframes."""
#     data_dfs['klines_1m_df'] = add_indicators(data_dfs['klines_1m_df'], INDICATORS_CONFIG)
#     data_dfs['klines_15m_df'] = add_indicators(data_dfs['klines_15m_df'], INDICATORS_CONFIG)
#     data_dfs['klines_1h_df'] = add_indicators(data_dfs['klines_1h_df'], INDICATORS_CONFIG)
#     data_dfs['klines_4h_df'] = add_indicators(data_dfs['klines_4h_df'], INDICATORS_CONFIG)
#     data_dfs['klines_1d_df'] = add_indicators(data_dfs['klines_1d_df'], INDICATORS_CONFIG)
#     return data_dfs
#
#
# # @task
# def save_data_to_db_task(ticker: str, data_dfs):
#     """Saves the dataframes to the database."""
#     functions_with_kwargs = [
#         (insert_records, dict(data=[KLine(**item) for item in data_dfs['klines_1m_df'].to_dict(orient="records")],
#                               table_name="klines_1m",
#                               conflict_columns=("open_time", "close_time"),
#                               db_name=ticker)),
#         (insert_records, dict(data=[KLine(**item) for item in data_dfs['klines_15m_df'].to_dict(orient="records")],
#                               table_name="klines_15m",
#                               conflict_columns=("open_time", "close_time"),
#                               db_name=ticker)),
#         (insert_records, dict(data=[KLine(**item) for item in data_dfs['klines_1h_df'].to_dict(orient="records")],
#                               table_name="klines_1h",
#                               conflict_columns=("open_time", "close_time"),
#                               db_name=ticker)),
#         (insert_records, dict(data=[KLine(**item) for item in data_dfs['klines_4h_df'].to_dict(orient="records")],
#                               table_name="klines_4h",
#                               conflict_columns=("open_time", "close_time"),
#                               db_name=ticker)),
#         (insert_records, dict(data=[KLine(**item) for item in data_dfs['klines_1d_df'].to_dict(orient="records")],
#                               table_name="klines_1d",
#                               conflict_columns=("open_time", "close_time"),
#                               db_name=ticker))
#     ]
#     parallel_execute(functions_with_kwargs)
#
#
# # @task
# def generate_images_task(ticker: str, data_dfs, save_to_s3: bool = False):
#     imgs_path = (IMAGES_PATH / ticker / datetime.datetime.now().strftime("%Y%m%dT%H%M%S"))
#     shutil.rmtree(imgs_path.as_posix(), ignore_errors=True)
#
#     functions_with_kwargs = [
#         (calc_swings_and_plot, dict(df=data_dfs['klines_1m_df'], ticker=ticker, interval="1m", save_path=imgs_path.as_posix())),
#         (calc_swings_and_plot, dict(df=data_dfs['klines_15m_df'], ticker=ticker, interval="15m", save_path=imgs_path.as_posix())),
#         (calc_swings_and_plot, dict(df=data_dfs['klines_1h_df'], ticker=ticker, interval="1h", save_path=imgs_path.as_posix())),
#         (calc_swings_and_plot, dict(df=data_dfs['klines_4h_df'], ticker=ticker, interval="4h", save_path=imgs_path.as_posix())),
#         (calc_swings_and_plot, dict(df=data_dfs['klines_1d_df'], ticker=ticker, interval="1d", save_path=imgs_path.as_posix())),
#     ]
#     parallel_execute(functions_with_kwargs)
#
#     image_files = sorted(imgs_path.glob("*.png"), key=lambda x: os.path.getctime(x))
#     image_files = [img.as_posix() for img in image_files]
#     image_path = (imgs_path / "concatenated.png").as_posix()
#     concat_images(image_files, image_path)
#     if save_to_s3:
#         # image_path, img_url = upload_to_minio(image_path, f"{ticker}{image_path.split(ticker)[1]}")
#         results = [upload_to_minio(image_file, f"{ticker}{image_file.split(ticker)[1]}")[0] for image_file in image_files]
#             # image_path, img_url = upload_to_minio(image_file, f"{ticker}{image_file.split(ticker)[1]}")
#     return {"images": results}
#
#
# # @task
# def send_plot_to_kafka_task(symbol, path_to_image):
#     producer = Producer({
#         'bootstrap.servers': KAFKA_SERVERS
#     })
#
#     message = {
#         'type': 'image',
#         'symbol': symbol,
#         'path': path_to_image
#     }
#
#     producer.produce(KAFKA_TOPIC, json.dumps(message).encode('utf-8'))
#     producer.flush()
#
#     return message
#
#
# # @flow(name="Prepare trading chart")
# def prepare_trading_chart_flow(ticker: str):
#     logger = setup_prefect_logging()
#
#     asset = ticker[-4:]
#     if asset not in ("USDT", "USDC"):
#         raise ValueError(f"Wrong asset: {asset}. Asset must be USDT, USDC.")
#
#     if not is_db_exists(ticker):
#         apply_schema(ticker)
#
#     data_dfs = collect_data_task(ticker)
#     data_dfs = calculate_features_task(data_dfs)
#     save_data_to_db_task(ticker, data_dfs)
#     images_path = generate_images_task(ticker, data_dfs, save_to_s3=True)
#     message = send_plot_to_kafka_task(ticker, path_to_image=images_path["images"])
#
#     logger.info(f"Trading chart prepared and sent: {message}")
#
#     return message
#
#
# if __name__ == "__main__":
#     img_path = prepare_trading_chart_flow("BNBUSDT")
#     print(img_path)