import pandas as pd

from src.core.technical_analysis.klines_buffer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.core.technical_analysis.data_structures.swings import <PERSON><PERSON><PERSON>, <PERSON>Low, Swing
from src.core.technical_analysis.data_structures.kline import KLine
from src.utils.db_utils import delete_records, fetch_last_n_records, insert_records


class StructuralSwingsDetector:
    swing_window: int = 2

    def __init__(self, df: pd.DataFrame, ticker: str, interval: str, limit: int):
        self.df = df
        self.ticker = ticker
        self.interval = interval
        self.limit = limit
        self.klines_buffer = KLinesBuffer([])
        self.klines_history = [KLine(**kline) for kline in df.to_dict(orient='records')]
        self._clear_swings_table()

    def _clear_swings_table(self) -> None:
        delete_records(f"swings_{self.interval}", where_statement="1", db_name=self.ticker)

    def get_last_price(self) -> float:
        return self.klines_buffer.get_last().close_price

    def get_structural_swings(self, limit: int = 100) -> list[Swing]:
        data = fetch_last_n_records(table_name=f"swings_{self.interval}",
                                    n=limit,
                                    order_col="dt",
                                    db_name=self.ticker)
        return [Swing(*item) for item in data]

    def get_last_klines(self, n: int = 5) -> list[KLine]:
        return list(reversed([KLine(*x) for x in self.df.sort_values(by='open_time', ascending=False).head(n).to_dict(orient='records')]))

    def get_last_swings(self, n: int = 1) -> list[Swing]:
        return list(reversed(fetch_last_n_records(table_name=f"swings_{self.interval}",
                                                  n=n,
                                                  order_col="dt",
                                                  db_name=self.ticker)))

    @property
    def last_swing(self) -> Swing | None:
        swings = self.get_last_swings(1)
        return swings[0] if swings else None

    def get_prev_candlestick(self) -> KLine:
        return self.klines_history.pop(0)

    def check_swing(self, klines: list[KLine]) -> Swing | None:
        if len(klines) != 2 * self.swing_window + 1:
            raise ValueError(f"Expected {2 * self.swing_window + 1} klines, got {len(klines)}")

        swing_candidate = klines[self.swing_window]

        if all(swing_candidate.high_price > kline.high_price for i, kline in enumerate(klines) if i != self.swing_window):
            return SwingHigh(dt=swing_candidate.open_time, value=swing_candidate.high_price)

        if all(swing_candidate.low_price < kline.low_price for i, kline in enumerate(klines) if i != self.swing_window):
            return SwingLow(dt=swing_candidate.open_time, value=swing_candidate.low_price)

        return None

    def find_structural_swing_step(self) -> None:
        klines = self.klines_buffer.get_last(2 * self.swing_window + 1)
        swing = self.check_swing(klines)

        if not swing:
            return

        if not self.last_swing:
            self._insert_swing(swing)
            return

        if isinstance(swing, SwingHigh) and isinstance(self.last_swing, SwingHigh):
            if swing.value > self.last_swing.value:
                self._replace_last_swing(swing)
        elif isinstance(swing, SwingLow) and isinstance(self.last_swing, SwingLow):
            if swing.value < self.last_swing.value:
                self._replace_last_swing(swing)
        else:
            self._insert_swing(swing)

    def _insert_swing(self, swing: Swing) -> None:
        insert_records(data=[swing], table_name=f"swings_{self.interval}", conflict_columns=["dt"], db_name=self.ticker)

    def _replace_last_swing(self, swing: Swing) -> None:
        delete_records(table_name=f"swings_{self.interval}",
                       where_statement=f"dt='{self.last_swing.dt}'",
                       db_name=self.ticker)
        self._insert_swing(swing)

    def cold_start(self) -> None:
        for _ in range(2 * self.swing_window + 1):
            kline = self.get_prev_candlestick()
            self.klines_buffer.append(kline)

    def run(self):
        self.cold_start()
        while self.klines_history:
            kline = self.get_prev_candlestick()
            self.klines_buffer.append(kline)
            self.find_structural_swing_step()
        return self
