import re
import ast
import yaml
import json
import requests

from src.config.openai import OPENAI_API_URL, OPENAI_API_HEADERS


def evaluate_response_with_llm(response: dict, payload: dict):
    llm_response = response['choices'][0]['message']
    validate_answer_prompt = {
        "role": "user",
        "content": "Please review your last response. Make sure it is correct. If not, please provide the correct answer."
    }
    payload['messages'].append(llm_response)
    payload['messages'].append(validate_answer_prompt)
    response = requests.post(OPENAI_API_URL, headers=OPENAI_API_HEADERS, json=payload).json()

    return response


def parse_response(response: dict):
    response = response['choices'][0]['message']['content']
    parsed_json = re.findall(r'\{(?:[^{}]*|\{(?:[^{}]*|\{[^{}]*\})*\})*\}', response, re.DOTALL)[0]
    try:
        parsed_json = ast.literal_eval(parsed_json)
    except ValueError:
        try:
            parsed_json = json.loads(parsed_json)
        except ValueError:
            parsed_json = {}
    return parsed_json


def shorten_json_data(data: list[dict]):
    if len(data) > 0:
        return {key: [item[key] for item in data] for key in data[0].keys()}

def yaml_snippet(data: list[dict]):
    return f"```yaml\n{yaml.dump(data, sort_keys=False, default_flow_style=False)}\n```"

def json_snippet(data: list[dict]):
    return f"```json\n{json.dumps(data, indent=4)}\n```"
