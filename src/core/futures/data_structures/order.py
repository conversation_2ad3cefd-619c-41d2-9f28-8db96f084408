import datetime
import pandas as pd

from dataclasses import dataclass, field, asdict
from typing import Any, Optional


@dataclass
class Order:
    order_id: Optional[int] = None
    symbol: Optional[str] = None
    status: Optional[str] = None
    client_order_id: Optional[str] = None
    price: Optional[float] = None
    avg_price: Optional[float] = None
    orig_qty: Optional[float] = None
    executed_qty: Optional[float] = None
    cum_quote: Optional[float] = None
    time_in_force: Optional[str] = None
    type: Optional[str] = None
    reduce_only: Optional[bool] = None
    close_position: Optional[bool] = None
    side: Optional[str] = None
    position_side: Optional[str] = None
    stop_price: Optional[float] = None
    working_type: Optional[str] = None
    price_protect: Optional[bool] = None
    orig_type: Optional[str] = None
    price_match: Optional[bool] = None
    self_trade_prevention_mode: Optional[str] = None
    good_till_date: Optional[str] = None
    time: Any = None  # Can handle datetime, pd.Timestamp, or ISO format strings
    update_time: Any = None  # Can handle datetime, pd.Timestamp, or ISO format strings

    def __post_init__(self):
        if isinstance(self.time, pd.Timestamp):
            self.time = self.time.to_pydatetime()
        elif isinstance(self.time, str):
            self.time = datetime.datetime.fromisoformat(self.time)

        if isinstance(self.update_time, pd.Timestamp):
            self.update_time = self.update_time.to_pydatetime()
        elif isinstance(self.update_time, str):
            self.update_time = datetime.datetime.fromisoformat(self.update_time)

    def dict(self):
        data = asdict(self)
        data = {k: v for k, v in data.items() if v is not None}

        if 'time' in data:
            data['time'] = self.time.strftime('%Y-%m-%d %H:%M:%S')
        if 'update_time' in data:
            data['update_time'] = self.update_time.strftime('%Y-%m-%d %H:%M:%S')

        for key in ['price', 'avg_price', 'orig_qty', 'executed_qty', 'cum_quote', 'stop_price']:
            if key in data:
                data[key] = round(data[key], 5)
        return data
