#%%
%load_ext autoreload
%autoreload 2
#%%
import pandas as pd
import numpy as np
import datetime

from src.binance_client import get_client

from src.core.technical_analysis.data_structures.kline import KLine
from src.utils.extract_data import get_klines_df
from src.core.technical_analysis.indicators import add_indicators
#%%

#%%
ticker: str = "SUIUSDT"
asset: str = "USDT"
client_name: str = "um_futures"
#%%
client = get_client(client_name)
#%%
client.ticker_price(symbol=ticker)#['symbol']
#%%
for i in range(1):
    current_price = float(client.ticker_price(symbol=ticker)['price'])
    server_time = datetime.datetime.fromtimestamp(client.time()['serverTime'] / 1000)
    current_kline_1m = KLine(*client.klines(symbol=ticker, interval="1m", limit=1)[0][:-1])
    print(server_time, current_price)
#%%
current_kline_1m.dict()
#%%

#%%
def timedelta64_to_seconds(td: np.timedelta64) -> float:
    return td.astype('timedelta64[s]').astype(float)
#%%
df = get_klines_df(ticker, '1m', 10000)
df = add_indicators(df)
# df['status'] = np.where(df['close_price'] > df['open_price'], "green", "red")
df['target'] = np.where(df['close_price'] > df['close_price'].shift(1), 1,0)
df = df.dropna().reset_index(drop=True)
#%%

#%%
klines = [KLine(*x[:-1]) for x in client.klines(symbol=ticker, interval="1m", limit=20)][::-1][:10]
klines[0]
#%%
x = [x.number_of_trades for x in klines]
x
#%%
np.mean(x[1:])
#%%
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score
from sklearn.model_selection import TimeSeriesSplit
#%%
data = df.copy()
data['open_time'] = pd.to_datetime(data['open_time'])
data.set_index('open_time', inplace=True)
#%%
# Separate features and target
X = data.drop(['target', 'close_time'], axis=1)
y = data['target']
#%%
tscv = TimeSeriesSplit(n_splits=5)
#%%
# Initialize lists to store results
accuracies = []
reports = []
#%%
# Perform time series cross-validation
for train_index, test_index in tscv.split(X):
    X_train, X_test = X.iloc[train_index], X.iloc[test_index]
    y_train, y_test = y.iloc[train_index], y.iloc[test_index]
    
    # Scale the features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Train the model
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train_scaled, y_train)
    
    # Make predictions
    y_pred = model.predict(X_test_scaled)
    
    # Evaluate the model
    accuracy = accuracy_score(y_test, y_pred)
    report = classification_report(y_test, y_pred)
    
    accuracies.append(accuracy)
    reports.append(report)

#%%
# Print average accuracy
print(f"Average Accuracy: {np.mean(accuracies):.4f}")

# Print the last classification report (for the most recent data)
print("\nClassification Report for the last fold:")
print(reports[-1])

# Feature importance
feature_importance = pd.DataFrame({'feature': X.columns, 'importance': model.feature_importances_})
feature_importance = feature_importance.sort_values('importance', ascending=False)
print("\nTop 10 Most Important Features:")
print(feature_importance.head(10))
#%%

#%%
import torch
from torch import nn
import pytorch_lightning as pl
from pytorch_lightning.callbacks import EarlyStopping
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import classification_report, accuracy_score
from torch.utils.data import Dataset, DataLoader
#%%

#%%
# Load the data
data = df.copy()

# Convert 'open_time' to datetime and set it as index
data['open_time'] = pd.to_datetime(data['open_time'])
data.set_index('open_time', inplace=True)

# Separate features and target
features = data.drop(['target', 'close_time'], axis=1)
target = data['target']

#%%
# Scale the features
scaler = StandardScaler()
features_scaled = scaler.fit_transform(features)
#%%
# Function to create sequences
def create_sequences(data, targets, sequence_length):
    X, y = [], []
    for i in range(len(data) - sequence_length):
        X.append(data[i:(i + sequence_length)])
        y.append(targets[i + sequence_length])
    return np.array(X), np.array(y)
#%%
# Set sequence length
sequence_length = 10

# Create sequences
X, y = create_sequences(features, target.values, sequence_length)
#%%
X.shape
#%%
class FinancialDataset(Dataset):
    def __init__(self, X, y):
        self.X = torch.FloatTensor(X)
        self.y = torch.FloatTensor(y)

    def __len__(self):
        return len(self.y)

    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]
#%%
# Create PyTorch Lightning Module
class LSTMModel(pl.LightningModule):
    def __init__(self, input_size, hidden_size, num_layers, output_size):
        super().__init__()
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_size, output_size)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        lstm_out = lstm_out[:, -1, :]
        out = self.fc(lstm_out)
        out = self.sigmoid(out)
        return out

    def training_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self(x)
        loss = nn.BCELoss()(y_hat, y.unsqueeze(1))
        self.log('train_loss', loss)
        return loss

    def validation_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self(x)
        loss = nn.BCELoss()(y_hat, y.unsqueeze(1))
        self.log('val_loss', loss)
        return loss

    def configure_optimizers(self):
        return torch.optim.Adam(self.parameters(), lr=0.001)
#%%
# Set up TimeSeriesSplit
tscv = TimeSeriesSplit(n_splits=5)

# Initialize lists to store results
accuracies = []
reports = []

# Perform time series cross-validation
for fold, (train_index, val_index) in enumerate(tscv.split(X)):
    print(f"Fold {fold + 1}")

    # Split data
    X_train, X_val = X[train_index], X[val_index]
    y_train, y_val = y[train_index], y[val_index]

    # Create datasets and dataloaders
    train_dataset = FinancialDataset(X_train, y_train)
    val_dataset = FinancialDataset(X_val, y_val)
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=32)

    # Initialize model
    model = LSTMModel(input_size=X.shape[2], hidden_size=64, num_layers=2, output_size=1)

    # Set up trainer
    early_stop_callback = EarlyStopping(monitor='val_loss', patience=5)
    trainer = pl.Trainer(max_epochs=50, callbacks=[early_stop_callback])

    # Train model
    trainer.fit(model, train_loader, val_loader)

    # Make predictions
    model.eval()
    with torch.no_grad():
        y_pred = model(torch.FloatTensor(X_val)).numpy()
    y_pred = (y_pred > 0.5).astype(int).flatten()

    # Evaluate the model
    accuracy = accuracy_score(y_val, y_pred)
    report = classification_report(y_val, y_pred)
    
    accuracies.append(accuracy)
    reports.append(report)

    print(f"Accuracy: {accuracy:.4f}")
    print("Classification Report:")
    print(report)
    print("\n")
#%%
# Print average accuracy
print(f"Average Accuracy: {np.mean(accuracies):.4f}")

# Print the last classification report (for the most recent data)
print("\nClassification Report for the last fold:")
print(reports[-1])

# Feature importance (using permutation importance)
from sklearn.inspection import permutation_importance

# Reshape X_val to 2D for permutation_importance
X_val_2d = X_val.reshape(X_val.shape[0], -1)

def predict_fn(X):
    X_tensor = torch.FloatTensor(X.reshape(-1, sequence_length, features.shape[1]))
    with torch.no_grad():
        return model(X_tensor).numpy().flatten()

perm_importance = permutation_importance(predict_fn, X_val_2d, y_val, n_repeats=10, random_state=42)

feature_importance = pd.DataFrame({'feature': features.columns.repeat(sequence_length),
                                   'importance': perm_importance.importances_mean})
feature_importance = feature_importance.groupby('feature').mean().sort_values('importance', ascending=False)

print("\nTop 10 Most Important Features:")
print(feature_importance.head(10))
#%%

#%%

#%%

#%%
