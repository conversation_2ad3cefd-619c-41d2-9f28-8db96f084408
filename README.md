# crypto-42

## Data pipeline 

![](docs/pipeline.png)


Link to [miro](https://miro.com/app/board/uXjVKzmeetg=/) 


## Graph example

![](docs/graph_example.png)

## Deploy

```shell
  docker network create app-shared-network
  
  docker stop $(docker ps -aq) && docker rm $(docker ps -aq)
  
  docker volume rm $(docker volume ls -q)
  
  docker network rm $(docker network ls -q --filter type=custom)
```