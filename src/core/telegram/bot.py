from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import Application, CommandHandler, ContextTypes, CallbackQueryHandler, ConversationHandler, MessageHandler, filters

from src.config.logs import setup_logging

# Define states for conversation
WAITING_FOR_TEST1_INPUT = 1
WAITING_FOR_TEST2_INPUT = 2

class TelegramBot:
    """A simplified Telegram Bot with two test methods."""
    
    def __init__(self, token):
        """Initialize the bot with the provided token."""
        self.token = token
        self.logger = setup_logging()
        
        # Create the application
        self.app = Application.builder().token(self.token).build()
        
        # Register handlers
        self._register_handlers()
    
    def _register_handlers(self):
        """Register command handlers."""
        # Command handlers
        self.app.add_handler(CommandHandler("start", self.start_command))
        self.app.add_handler(CommandHandler("test1", self.test1_command))
        self.app.add_handler(CommandHandler("test2", self.test2_command))
        
        # Conversation handler for test1
        test1_conv_handler = ConversationHandler(
            entry_points=[CallbackQueryHandler(self.test1_button, pattern='^test1$')],
            states={
                WAITING_FOR_TEST1_INPUT: [MessageHandler(filters.TEXT & ~filters.COMMAND, self.test1_input)]
            },
            fallbacks=[CommandHandler("cancel", self.cancel)]
        )
        self.app.add_handler(test1_conv_handler)
        
        # Conversation handler for test2
        test2_conv_handler = ConversationHandler(
            entry_points=[CallbackQueryHandler(self.test2_button, pattern='^test2$')],
            states={
                WAITING_FOR_TEST2_INPUT: [MessageHandler(filters.TEXT & ~filters.COMMAND, self.test2_input)]
            },
            fallbacks=[CommandHandler("cancel", self.cancel)]
        )
        self.app.add_handler(test2_conv_handler)
        
        # IMPORTANT: This handler must be added LAST to catch any callbacks not handled by conversation handlers
        self.app.add_handler(CallbackQueryHandler(self.button_callback))
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Send a message with buttons when the command /start is issued."""
        keyboard = [
            [InlineKeyboardButton("Convert to UPPERCASE", callback_data="test1")],
            [InlineKeyboardButton("Concatenate strings", callback_data="test2")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.message.reply_text("Please choose an option:", reply_markup=reply_markup)
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle button callbacks not handled by conversation handlers."""
        query = update.callback_query
        await query.answer()
        self.logger.info(f"Received callback with data: {query.data}")
    
    async def test1_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """Handle test1 button press."""
        query = update.callback_query
        await query.answer()
        self.logger.info("Test1 button pressed")
        await query.edit_message_text(text="Please enter a string to convert to uppercase:")
        return WAITING_FOR_TEST1_INPUT
    
    async def test1_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """Process the input for test1."""
        input_string = update.message.text
        result = self.test1(input_string)
        await update.message.reply_text(f"Result: {result}")
        await self.send_menu(update, context)
        return ConversationHandler.END
    
    async def test2_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """Handle test2 button press."""
        query = update.callback_query
        await query.answer()
        self.logger.info("Test2 button pressed")
        await query.edit_message_text(text="Please enter two strings separated by a space:")
        return WAITING_FOR_TEST2_INPUT
    
    async def test2_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """Process the input for test2."""
        text = update.message.text
        parts = text.split(' ', 1)
        
        if len(parts) < 2:
            await update.message.reply_text("You need to provide two strings separated by a space.")
            return WAITING_FOR_TEST2_INPUT
            
        string1 = parts[0]
        string2 = parts[1]
        result = self.test2(string1, string2)
        await update.message.reply_text(f"Result: {result}")
        await self.send_menu(update, context)
        return ConversationHandler.END
    
    async def cancel(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """Cancel the conversation."""
        await update.message.reply_text("Operation cancelled.")
        await self.send_menu(update, context)
        return ConversationHandler.END
    
    async def send_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Send the main menu."""
        keyboard = [
            [InlineKeyboardButton("Convert to UPPERCASE", callback_data="test1")],
            [InlineKeyboardButton("Concatenate strings", callback_data="test2")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.message.reply_text("Please choose an option:", reply_markup=reply_markup)
    
    async def test1_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /test1 command."""
        if not context.args:
            await update.message.reply_text('Please provide a string. Example: /test1 hello')
            return
        
        input_string = ' '.join(context.args)
        result = self.test1(input_string)
        await update.message.reply_text(result)
    
    async def test2_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /test2 command."""
        if len(context.args) < 2:
            await update.message.reply_text('Please provide two strings. Example: /test2 hello world')
            return
        
        string1 = context.args[0]
        string2 = ' '.join(context.args[1:])
        result = self.test2(string1, string2)
        await update.message.reply_text(result)
    
    def test1(self, input_string):
        """Convert input string to uppercase."""
        return input_string.upper()
    
    def test2(self, string1, string2):
        """Concatenate two strings."""
        return string1 + string2
    
    def run(self):
        """Start the bot."""
        self.app.run_polling()
        self.logger.info("Bot started")
