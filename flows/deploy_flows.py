from pathlib import Path

from flows.prepare_llm_request import prepare_llm_request_flow
from flows.predict_next_kline import ml_pipeline_flow

from src.config.base import DEFAULT_TRAINING_CONFIG


# if __name__ == "__main__":
#     ticker = "ETHUSDT"
#     ml_config = DEFAULT_TRAINING_CONFIG.copy()
#     ml_config["ticker"] = ticker
#
#
#     prepare_llm_request_flow.from_source(
#         source=Path(__file__).parent,
#         entrypoint="prepare_llm_request.py:prepare_llm_request_flow",
#     ).deploy(
#         name="prepare-llm-request",
#         work_pool_name="default-pool",
#         cron="0 * * * *",
#         tags=["technical-analysis"],
#         job_variables={
#             "env": {
#                 "KAFKA_SERVERS": "kafka:9092",
#                 "MINIO_SERVER": "minio:9000",
#             },
#         },
#         parameters={
#             "ticker": ticker
#         }
#     )
#
#     ml_pipeline_flow.from_source(
#         source=Path(__file__).parent,
#         entrypoint="predict_next_kline.py:ml_pipeline_flow",
#     ).deploy(
#         name="predict-next-kline",
#         work_pool_name="default-pool",
#         cron="*/15 * * * *",
#         tags=["machine-learning"],
#         job_variables={
#             "env": {"KAFKA_SERVERS": "kafka:9092"},
#         },
#         parameters={
#             "ticker": ticker,
#             "config": ml_config
#         }
#     )