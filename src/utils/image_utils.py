import base64

from pathlib import Path
from PIL import Image

from src.s3_client import s3client
from src.config.base import MINIO_ENDPOINT_URL, MINIO_BUCKET


def concat_images(image_paths: list[str], output_path: str = None, orientation: str = 'vertical'):
    images = [Image.open(path) for path in image_paths]
    widths, heights = zip(*(img.size for img in images))

    total_width = max(widths)
    total_height = sum(heights)
    new_image = Image.new('RGB', (total_width, total_height))

    if orientation == 'vertical':
        y_offset = 0
        for img in images:
            new_image.paste(img, (0, y_offset))
            y_offset += img.height
    elif orientation == 'horizontal':
        x_offset = 0
        for img in images:
            new_image.paste(img, (x_offset, 0))
            x_offset += img.width

    if output_path:
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        new_image.save(output_path)
        return output_path
    else:
        return new_image


def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


def upload_to_minio(src_path, dst_path, bucket: str = MINIO_BUCKET, expires_in: int = 3600):
    client = s3client()
    client.upload_file(src_path, bucket, dst_path)

    image_url = f"{MINIO_ENDPOINT_URL}/{bucket}/{dst_path}"

    if expires_in:
        image_url = client.generate_presigned_url(
            'get_object',
            Params={'Bucket': bucket, 'Key': dst_path},
            ExpiresIn=expires_in
        )

    return dst_path, image_url
