import argparse
import time
import json
from binance.websocket.um_futures.websocket_client import UMFuturesWebsocketClient

from src.core.technical_analysis.data_structures.kline import KLine
from src.core.technical_analysis.klines_buffer import KLinesBuffer
from src.core.realtime.rules import RuleEngine
from src.core.realtime.kafka_producer import KafkaProducer
from src.config.logs import setup_logging
from src.config.base import KAFKA_TOPIC


logger = setup_logging()


def handle_kline_message(buffer, rule_engine, log=True):
    """Factory function to create a closure that handles incoming WebSocket messages."""

    def _handle_message(_, message):
        try:
            payload = json.loads(message)

            if payload.get('e') == 'kline':
                kline_data = payload['k']

                kline_object = KLine(
                    open_time=kline_data['t'],
                    open_price=kline_data['o'],
                    high_price=kline_data['h'],
                    low_price=kline_data['l'],
                    close_price=kline_data['c'],
                    volume=kline_data['v'],
                    close_time=kline_data['T'],
                    quote_asset_volume=kline_data['q'],
                    number_of_trades=kline_data['n'],
                    taker_buy_base_asset_volume=kline_data['V'],
                    taker_buy_quote_asset_volume=kline_data['Q'],
                    symbol=kline_data['s'],
                    interval=kline_data['i'],
                    is_closed=kline_data['x'],
                    event_time=payload['E']
                )

                buffer.append(kline_object)
                rule_engine.process_kline(kline_object)

                if log:
                    logger.info(f"KLine received: {kline_object.dict()}")
                    if kline_object.is_closed:
                        logger.info(f"KLINE CLOSED: Symbol={kline_object.symbol}, "
                                    f"Close={kline_object.close_price}, "
                                    f"Status={kline_object.status}")

            elif 'result' in payload and payload['result'] is None:
                logger.info(f"Subscription confirmation: {payload}")
            else:
                logger.warning(f"Unexpected message received: {payload}")

        except json.JSONDecodeError:
            logger.error(f"JSON decoding failed: {message}")
        except KeyError as e:
            logger.error(f"KeyError ({e}) in payload: {payload}")
        except Exception as e:
            logger.error(f"{type(e).__name__} error: {e} - Raw message: {message}")

    return _handle_message


def main(symbol: str, interval: str = '1m'):
    producer = KafkaProducer(topic=KAFKA_TOPIC)
    buffer = KLinesBuffer(max_size=100)
    rule_engine = RuleEngine(producer=producer, buffer=buffer)

    client = UMFuturesWebsocketClient(
        on_message=handle_kline_message(buffer, rule_engine)
    )

    logger.info(f"Subscribing to {symbol}@{interval} Kline stream...")
    client.kline(symbol=symbol.lower(), interval=interval)

    logger.info("WebSocket connected. Listening... Press Ctrl+C to stop.")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Interrupted by user.")
    finally:
        client.stop()
        logger.info("WebSocket closed gracefully.")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Binance KLine WebSocket Client')
    parser.add_argument('--symbol', type=str, default='BTCUSDT', help='Trading symbol (default: BTCUSDT)')
    parser.add_argument('--interval', type=str, default='1m', help='Kline interval (default: 1m)')

    args = parser.parse_args()
    main(args.symbol, args.interval)
