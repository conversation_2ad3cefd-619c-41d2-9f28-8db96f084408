#%%
%load_ext autoreload
%autoreload 2
#%%
import os
import gc
import shutil
import json
import datetime
import requests

from src.binance_client import get_server_time, get_client
from src.config import (IMAGES_PATH, OPENAI_API_HEADERS, INTERVAL_LIMIT_MAPPING, DROP_COLUMNS, DATA_PATH,
                        OPENAI_API_PAYLOAD, OPENAI_API_URL)
from src.config.logs import setup_jupyter_logging
from src.core.futures.data_structures import Order, OpenPosition, ClosedPosition, Balance
from src.core.llm.utils import parse_response, shorten_json_data, yaml_snippet
from src.core.llm.data_structures.llm_response import LLMResponse
from src.core.technical_analysis.indicators import add_indicators
from src.core.technical_analysis.data_structures import KLine
from src.utils.extract_data import (get_klines_df, get_orders_df, get_open_positions_df, get_position_history_df,
                                    get_balance_df, get_current_price)
from src.utils.image_utils import concat_images, encode_image
from src.utils.db_utils.types import df2dataclass
from src.utils.db_utils import insert_records, fetch_last_n_records
from src.misc.utils import parallel_execute
from src.utils.visualisation import calc_swings_and_plot

from tasks.apply_schema import apply_schema
#%%
ticker: str = "SUIUSDT"
asset: str = "USDT"
client_name: str = "um_futures"
#%%
if not os.path.exists(os.path.join(DATA_PATH, 'sqlite', f'{ticker}.db')):
    apply_schema(ticker)
#%%
setup_jupyter_logging()
#%%
client = get_client(client_name)
#%%

#%%

#%%
## Collect data
#%%
functions_with_kwargs = [
    (get_open_positions_df, dict(symbol=ticker, client_name=client_name, drop_columns=DROP_COLUMNS["open_positions_df"])),
    (get_position_history_df, dict(symbol=ticker, client_name=client_name, drop_columns=DROP_COLUMNS["position_history_df"])),
    (get_orders_df, dict(symbol=ticker, client_name=client_name, drop_columns=DROP_COLUMNS["open_orders_df"])),
    (get_klines_df, dict(symbol=ticker, interval="1m", limit=INTERVAL_LIMIT_MAPPING["1m"] + 100)),
    (get_klines_df, dict(symbol=ticker, interval="15m", limit=INTERVAL_LIMIT_MAPPING["15m"] + 100)),
    (get_klines_df, dict(symbol=ticker, interval="1h", limit=INTERVAL_LIMIT_MAPPING["1h"] + 100)),
    (get_klines_df, dict(symbol=ticker, interval="4h", limit=INTERVAL_LIMIT_MAPPING["4h"] + 100)),
    (get_klines_df, dict(symbol=ticker, interval="1d", limit=INTERVAL_LIMIT_MAPPING["1d"] + 100)),
    (get_balance_df, dict(asset=asset))
]
results = parallel_execute(functions_with_kwargs)
open_positions_df, position_history_df, open_orders_df, klines_1m_df, klines_15m_df, klines_1h_df, klines_4h_df, klines_1d_df, balance_df = results
del results
gc.collect()
#%%

#%%
## Calculate features
#%%
klines_1m_df = add_indicators(klines_1m_df)
klines_15m_df = add_indicators(klines_15m_df)
klines_1h_df = add_indicators(klines_1h_df)
#%%

#%%
## Find swings and save data
#%%
functions_with_kwargs = [
    (insert_records, dict(data=[KLine(**item) for item in klines_1m_df.to_dict(orient="records")],
                            table_name="klines_1m",
                            conflict_columns=("open_time", "close_time"))),
    (insert_records, dict(data=[KLine(**item) for item in klines_15m_df.to_dict(orient="records")],
                            table_name="klines_15m",
                            conflict_columns=("open_time", "close_time"))),
    (insert_records, dict(data=[KLine(**item) for item in klines_1h_df.to_dict(orient="records")],
                            table_name="klines_1h",
                            conflict_columns=("open_time", "close_time"))),
    (insert_records, dict(data=[KLine(**item) for item in klines_4h_df.to_dict(orient="records")],
                            table_name="klines_4h",
                            conflict_columns=("open_time", "close_time"))),
    (insert_records, dict(data=[KLine(**item) for item in klines_1d_df.to_dict(orient="records")],
                            table_name="klines_1d",
                            conflict_columns=("open_time", "close_time"))),

    (insert_records, dict(data=[Order(**item) for item in open_orders_df.to_dict(orient="records")],
                            table_name="orders",
                            conflict_columns=("order_id", "client_order_id"))),
    (insert_records, dict(data=[OpenPosition(**item) for item in open_positions_df.to_dict(orient="records")],
                            table_name="open_positions",
                            conflict_columns=("symbol", "position_side"))),
    (insert_records, dict(data=[ClosedPosition(**item) for item in position_history_df.to_dict(orient="records")],
                            table_name="closed_positions")),
]
parallel_execute(functions_with_kwargs)
gc.collect()
#%%
shutil.rmtree(IMAGES_PATH / ticker, ignore_errors=True)
functions_with_kwargs = [
    (calc_swings_and_plot, dict(df=klines_1m_df, ticker=ticker, interval="1m")),
    (calc_swings_and_plot, dict(df=klines_15m_df, ticker=ticker, interval="15m")),
    # (calc_swings_and_plot, dict(df=klines_1h_df, ticker=ticker, interval="1h")),
]
parallel_execute(functions_with_kwargs)
gc.collect()

image_files = sorted((IMAGES_PATH / ticker).glob("*.png"), key=lambda x: os.path.getctime(x))
image_files = [img.as_posix() for img in image_files]
image_path = IMAGES_PATH / ticker / "concated.png"
concat_images(image_files, image_path)
#%%

#%%
## Prepare prompt data
#%%
base64_image = encode_image(image_path)

functions_with_kwargs = [
    (df2dataclass, dict(df=open_positions_df,
                        dataclass=OpenPosition,
                        drop_columns=[])),
    (df2dataclass, dict(df=position_history_df,
                        dataclass=ClosedPosition,
                        drop_columns=[])),
    (df2dataclass, dict(df=open_orders_df,
                        dataclass=Order,
                        drop_columns=[])),
    (df2dataclass, dict(df=klines_1m_df,
                        dataclass=KLine,
                        drop_columns=['quote_asset_volume', 'number_of_trades', 'taker_buy_base_asset_volume',
                                        'taker_buy_quote_asset_volume'])),
    (df2dataclass, dict(df=klines_15m_df,
                        dataclass=KLine,
                        drop_columns=['quote_asset_volume', 'number_of_trades', 'taker_buy_base_asset_volume',
                                        'taker_buy_quote_asset_volume'])),
    (df2dataclass, dict(df=klines_1h_df,
                        dataclass=KLine,
                        drop_columns=['quote_asset_volume', 'number_of_trades', 'taker_buy_base_asset_volume',
                                        'taker_buy_quote_asset_volume'])),
    (df2dataclass, dict(df=balance_df,
                        dataclass=Balance,
                        drop_columns=["account_alias", "cross_wallet_balance", "cross_un_pnl",
                                        "max_withdraw_amount", "margin_available", "update_time"])),
]
results = parallel_execute(functions_with_kwargs)
open_positions, closed_positions, open_orders, klines_1m, klines_15m, klines_1h, balance = results
del results
gc.collect()
#%%
# sort from newest to oldest
open_positions = [x.dict() for x in open_positions][::-1]
closed_positions = [x.dict() for x in closed_positions][::-1]
open_orders = [x.dict() for x in open_orders][::-1]
klines_1m = [x.dict() for x in klines_1m][-10:][::-1]
klines_15m = [x.dict() for x in klines_15m][-5:][::-1]
klines_1h = [x.dict() for x in klines_1h][-5:][::-1]
balance = [x.dict() for x in balance]
#%%
history_response_history = [LLMResponse(*item).dict() for item in
                            fetch_last_n_records("llm_response_history", 10, "response_time", ticker)]

server_time = get_server_time(to_str=True)
server_price = float(get_current_price(ticker)['price'])
#%%
with open(DATA_PATH / "prompts" / "response_example.json", "rb") as f:
    response_example = json.load(f)

# with open(DATA_PATH / "prompts" / "system_message_content.txt", "rb") as f:
#     system_message_content = f.read().decode("utf-8")
#
# with open(DATA_PATH / "prompts" / "user_message_content.txt", "rb") as f:
#     user_message_content = f.read().decode("utf-8")

with open(DATA_PATH / "prompts" / "system_message_content_v2.md", "rb") as f:
    system_message_content = f.read().decode("utf-8")

with open(DATA_PATH / "prompts" / "user_message_content_v2.md", "rb") as f:
    user_message_content = f.read().decode("utf-8")
#%%
system_message_content = system_message_content#.format(response_example=response_example)
user_message_content = user_message_content.format(
    ticker=ticker,
    server_price=server_price,
    server_time=server_time,
    previous_responses=yaml_snippet(history_response_history),
    closed_positions=yaml_snippet(closed_positions),
    open_positions=yaml_snippet(open_positions),
    open_orders=yaml_snippet(open_orders),
    klines_1m=yaml_snippet(shorten_json_data(klines_1m)),
    klines_15m=yaml_snippet(shorten_json_data(klines_15m)),
    klines_1h=yaml_snippet(shorten_json_data(klines_1h)),
    balance=yaml_snippet(balance[0]),
)
#%%
print(user_message_content)
#%%
## Send LLM request
#%%
payload = OPENAI_API_PAYLOAD.copy()
payload['messages'][0]['content'] = system_message_content
payload['messages'][1]['content'][0]['text'] = user_message_content
payload['messages'][1]['content'][1]['image_url']['url'] = f"data:image/png;base64,{base64_image}"

response = requests.post(OPENAI_API_URL, headers=OPENAI_API_HEADERS, json=payload).json()

#%%
# response = evaluate_response_with_llm(response, payload)
#%%
# print(system_message_content)
#%%
parsed_response = parse_response(response)
parsed_response
#%%
choice, order = parsed_response['choice'], parsed_response['order']
parsed_response['response_time'] = datetime.datetime.fromtimestamp(response['created'])
parsed_response['order'] = json.dumps(order, indent=2)
insert_records([LLMResponse(**parsed_response)], "llm_response_history", conflict_columns=("response_time",), db_name=ticker)
parsed_response['order'] = order
#%%
# handle_choice(choice, ticker, order, parsed_response, open_orders)
#%%

#%%
