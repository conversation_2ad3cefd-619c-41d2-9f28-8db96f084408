import datetime
import logging
import functools
import traceback

import pandas as pd

from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from typing import Callable, Any

from src.core.technical_analysis.structural_swings_detector import StructuralSwingsDetector
from src.utils.plots import plot_candlesticks
from src.config.base import IMAGES_PATH, INDICATORS_CONFIG, INTERVAL_LIMIT_MAPPING


def is_valid_iso_datetime(s: str) -> bool:
    try:
        datetime.datetime.fromisoformat(s)
        return True
    except ValueError:
        return False


def df2dataclass(df: pd.DataFrame, dataclass: callable, drop_columns: list = []):
    if not df.empty:
        return [dataclass(**x) for x in df.drop(labels=drop_columns, axis=1).to_dict(orient="records")]
    return []


def exception_handler(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logging.error(traceback.format_exc())
            return None
    return wrapper


def parallel_execute(functions_with_kwargs: list[tuple[Callable, dict[str, Any]]]) -> list[Any]:
    """
    Execute a list of functions in parallel.

    Parameters:
    functions_with_kwargs (list): A list of tuples where each tuple contains a function and a dictionary of keyword arguments.

    Returns:
    list: A list of results from the function calls, in the same order as the input.
    """
    results = [None] * len(functions_with_kwargs)
    futures = []

    with ThreadPoolExecutor() as executor:
        for i, (func, kwargs) in enumerate(functions_with_kwargs):
            futures.append((i, executor.submit(func, **kwargs)))

        for i, future in futures:
            try:
                results[i] = future.result()
            except Exception as exc:
                results[i] = str(exc)

    return results


def calc_swings_and_plot(df, ticker, interval, save_path=None, show=False):
    limit = INTERVAL_LIMIT_MAPPING.get(interval, 100)

    # Detect swings
    ssd = StructuralSwingsDetector(df=df, ticker=ticker, interval=interval, limit=limit).run()
    klines = ssd.klines_buffer.get_last(limit)

    if interval == "1m":
        swings = None
    else:
        swings = ssd.get_structural_swings()

    # Handle save_path
    save_path = Path(save_path) if save_path else (IMAGES_PATH / ticker / "tmp")
    save_path.mkdir(parents=True, exist_ok=True)

    plot_file = save_path / f"{interval}_{limit}.png"

    indicators = [
        *(f"sma_{d['window']}" for d in INDICATORS_CONFIG["sma"]),
        *(f"ema_{d['span']}" for d in INDICATORS_CONFIG["ema"]),
        # Add other indicators here if necessary
        "bb_upper",
        "bb_middle",
        "bb_lower",
        # "price_channel_high",
        # "price_channel_low",
        "vwap",
        # "fibonacci_levels",
        # "trade_spike",
    ]

    plot_candlesticks(
        klines,
        swings,
        ticker=ticker,
        interval=interval,
        save_path=str(plot_file),
        width=1920,
        height=1080,
        show=show,
        add_indicators=indicators
    )

    return str(plot_file)


def prepare_indicators_image(ticker: str, interval: str):
    from src.utils.extract_data import get_klines_df
    from src.core.technical_analysis.indicators import add_indicators
    from src.config.base import INDICATORS_CONFIG

    
    if interval not in INTERVAL_LIMIT_MAPPING.keys():
        raise ValueError(f"Invalid interval: {interval}")

    df = get_klines_df(symbol=ticker, interval=interval, limit=INTERVAL_LIMIT_MAPPING[interval] + 100)
    df = add_indicators(df, INDICATORS_CONFIG)

    img_path = calc_swings_and_plot(df=df, ticker=ticker, interval=interval)

    return img_path


if __name__ == '__main__':
    for i in ["1m", "15m", "1h", "4h", "1d"]:
        prepare_indicators_image("SOLUSDT", i)