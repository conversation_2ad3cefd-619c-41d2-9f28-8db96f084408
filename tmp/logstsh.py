import socket
import json

# Test connection to Logstash
try:
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.connect(("127.0.0.1", 5400))
    test_message = json.dumps({"message": "Test log message", "level": "INFO"}) + "\n"
    s.sendall(test_message.encode())
    s.close()
    print("Successfully sent test message to Logstash")
except Exception as e:
    print(f"Failed to connect to Logstash: {e}")