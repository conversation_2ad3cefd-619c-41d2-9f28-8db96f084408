import datetime
import pandas as pd

from dataclasses import dataclass, field, asdict
from typing import Any, Optional


@dataclass
class Position:
    symbol: str
    quantity: float
    entry_price: float
    position_side: str
    quantity_usd: float
    position_open_time: Any

    def __post_init__(self):
        self._convert_position_open_time()

    def _convert_position_open_time(self):
        if isinstance(self.position_open_time, pd.Timestamp):
            self.position_open_time = self.position_open_time.to_pydatetime()
        elif isinstance(self.position_open_time, str):
            self.position_open_time = datetime.datetime.fromisoformat(self.position_open_time)


@dataclass
class ClosedPosition(Position):
    exit_price: float
    pnl: float
    position_close_time: Any

    mark_price: Optional[float] = None
    unrealized_profit: Optional[float] = None
    liquidation_price: Optional[float] = None
    leverage: Optional[int] = None
    max_notional_value: Optional[float] = None
    margin_type: Optional[str] = None
    isolated_margin: Optional[float] = None
    is_auto_add_margin: Optional[bool] = None
    isolated_wallet: Optional[float] = None
    isolated: Optional[bool] = None
    adl_quantile: Optional[int] = None

    def __post_init__(self):
        super().__post_init__()
        self._convert_position_close_time()

    def _convert_position_close_time(self):
        if isinstance(self.position_close_time, pd.Timestamp):
            self.position_close_time = self.position_close_time.to_pydatetime()
        elif isinstance(self.position_close_time, str):
            self.position_close_time = datetime.datetime.fromisoformat(self.position_close_time)

    def dict(self):
        data = asdict(self)
        data = {k: v for k, v in data.items() if v is not None}

        if 'position_open_time' in data:
            data['position_open_time'] = self.position_open_time.strftime('%Y-%m-%d %H:%M:%S')
        if 'position_close_time' in data:
            data['position_close_time'] = self.position_close_time.strftime('%Y-%m-%d %H:%M:%S')

        for key in ['quantity', 'entry_price', 'exit_price', 'mark_price', 'unrealized_profit', 'liquidation_price', 'quantity_usd', 'max_notional_value', 'isolated_margin', 'isolated_wallet']:
            if key in data:
                try:
                    data[key] = round(data[key], 5)
                except:
                    print(key)
                # data[key] = round(data[key], 5)
        return data


@dataclass
class OpenPosition(Position):
    break_even_price: float

    mark_price: Optional[float] = None
    unrealized_profit: Optional[float] = None
    liquidation_price: Optional[float] = None
    leverage: Optional[int] = None
    max_notional_value: Optional[float] = None
    margin_type: Optional[str] = None
    isolated_margin: Optional[float] = None
    is_auto_add_margin: Optional[bool] = None
    isolated_wallet: Optional[float] = None
    isolated: Optional[bool] = None
    adl_quantile: Optional[int] = None

    def __post_init__(self):
        super().__post_init__()

    def dict(self):
        data = asdict(self)
        data = {k: v for k, v in data.items() if v is not None}

        if 'position_open_time' in data:
            data['position_open_time'] = self.position_open_time.strftime('%Y-%m-%d %H:%M:%S')

        for key in ['quantity', 'entry_price', 'break_even_price', 'mark_price', 'unrealized_profit', 'liquidation_price', 'quantity_usd', 'max_notional_value', 'isolated_margin', 'isolated_wallet']:
            if key in data:
                data[key] = round(data[key], 5)
        return data