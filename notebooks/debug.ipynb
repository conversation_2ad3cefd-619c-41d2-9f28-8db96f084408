#%%
import pandas as pd
%load_ext autoreload
%autoreload 2
#%%
import os
import sys

sys.path.append('..')
#%%
import pandas as pd
#%%
from src.binance_client import get_server_time
from src.utils.db_utils import apply_schema
from src.config.base import DB_PATH, INDICATORS_CONFIG

from src.utils.extract_data import (get_klines_df, get_orders_df, get_open_positions_df, get_position_history_df,
                                    get_balance_df, get_current_price)

from src.utils.misc import parallel_execute, INTERVAL_LIMIT_MAPPING, INDICATORS_CONFIG
from src.core.technical_analysis.indicators import add_indicators
#%%
# ticker, asset = "ETHUSDT", "USDT"
ticker, asset = "BNBUSDT", "USDT"
#%%
get_current_price(ticker)
#%%
if not os.path.exists(os.path.join(DB_PATH, f'{ticker}.db')):
    apply_schema(ticker)
#%%
# def collect_data(ticker: str, asset: str):
#     functions_with_kwargs = [
#         (get_klines_df, dict(symbol=ticker, interval="1m", limit=INTERVAL_LIMIT_MAPPING["1m"] + 1)),
#         (get_klines_df, dict(symbol=ticker, interval="15m", limit=INTERVAL_LIMIT_MAPPING["15m"] + 1)),
#         (get_klines_df, dict(symbol=ticker, interval="1h", limit=INTERVAL_LIMIT_MAPPING["1h"] + 1)),
#         (get_klines_df, dict(symbol=ticker, interval="4h", limit=INTERVAL_LIMIT_MAPPING["4h"] + 1)),
#         (get_klines_df, dict(symbol=ticker, interval="1d", limit=INTERVAL_LIMIT_MAPPING["1d"] + 1)),
#         (get_balance_df, dict(asset=asset))
#     ]
#     results = parallel_execute(functions_with_kwargs)
#     data_dfs = dict(zip([
#         'klines_1m_df', 'klines_15m_df', 'klines_1h_df', 'klines_4h_df',
#         'klines_1d_df', 'balance_df'
#     ], results))
#     return data_dfs
#
# data_dfs = collect_data(ticker, asset)
# klines_1m_df, klines_15m_df, klines_1h_df, klines_4h_df, klines_1d_df, balance_df = data_dfs.values()
#%%

#%%
def add_dt_features(df: pd.DataFrame, dt_column: str = "open_time", inplace: bool = False):
    if not inplace:
        df = df.copy()
        
    df['year'] = df[dt_column].dt.year
    df['month'] = df[dt_column].dt.month
    df['day'] = df[dt_column].dt.day
    df['hour'] = df[dt_column].dt.hour
    df['minute'] = df[dt_column].dt.minute
    df['day_of_week'] = df[dt_column].dt.dayofweek  # 0 is Monday, 6 is Sunday
    
    if inplace:
        return None
    else:
        return df
#%%
klines_15m_df = get_klines_df(symbol=ticker, interval="15m", limit=1000)
#%%
df = add_indicators(klines_15m_df, INDICATORS_CONFIG)
# df = add_dt_features(df, dt_column="open_time", inplace=False)
#%%
klines_15m_df.columns
#%%
df.columns
#%%
df['target'] = (df['close_price'].shift(-1) > df['open_price'].shift(-1)).astype(int)
drop_columns = ['close_time','close_price', 'high_price', 'low_price', 'open_price', 'year', 'month', 'day',
                'ichimoku_senkou_span_a', 'ichimoku_senkou_span_b','ichimoku_chikou_span']

df = df.drop(drop_columns, axis=1)
df = df.dropna().reset_index(drop=True)
df = df.set_index('open_time')
print(df.shape)
#%%
print(df['target'].value_counts(normalize=True))
#%%
df
#%%
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report

import matplotlib.pyplot as plt
import seaborn as sns
#%%
# Features and label
X = df.drop(columns='target')
y = df['target']

# Train/test split (time-aware split)
X_train, X_test = X.iloc[:-2000], X.iloc[-2000:]
y_train, y_test = y.iloc[:-2000], y.iloc[-2000:]

# Train deeper forest
rf = RandomForestClassifier(n_estimators=200, random_state=42)
rf.fit(X_train, y_train)

# Re-evaluate
y_pred = rf.predict(X_test)
print(classification_report(y_test, y_pred))

# Feature importances
importances = pd.Series(rf.feature_importances_, index=X_train.columns)
importances = importances.sort_values(ascending=False)

# Plot top features
plt.figure(figsize=(10, 8))
importances.head(20).plot(kind='barh')
plt.gca().invert_yaxis()
plt.title("Top 20 Feature Importances")
plt.tight_layout()
plt.show()
#%%

#%%
lag_features = ['stoch_k', 'obv', 'cci', 'rsi', 'macd_hist']
num_lags = 3

for feature in lag_features:
    for lag in range(1, num_lags + 1):
        df[f'{feature}_lag{lag}'] = df[feature].shift(lag)

df = df.dropna()
print(df.shape)
#%%
# Re-split features and target
X = df.drop(columns='target')
y = df['target']

# Time-aware split
X_train, X_test = X.iloc[:-2000], X.iloc[-2000:]
y_train, y_test = y.iloc[:-2000], y.iloc[-2000:]
#%%
from xgboost import XGBClassifier
from sklearn.metrics import classification_report

model = XGBClassifier(n_estimators=200, max_depth=5, learning_rate=0.05, eval_metric='logloss')
model.fit(X_train, y_train)

y_pred = model.predict(X_test)
print(classification_report(y_test, y_pred))
#%%
# print(pd.DataFrame([model.get_booster().get_score(importance_type="gain")]).T.reset_index().sort_values(by=0, ascending=False).reset_index(drop=True).head(20).to_markdown())
#%%

#%%

#%%

#%%

#%%

#%%
list(range(1, 3))
#%%
