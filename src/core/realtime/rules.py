import numpy as np

from typing import List

from src.config.logs import setup_logging
from src.core.technical_analysis.klines_buffer import <PERSON><PERSON><PERSON><PERSON>uffer
from src.core.realtime.kafka_producer import KafkaProducer
from src.core.technical_analysis.data_structures.kline import KLine


class BaseRule:
    def evaluate(self, recent_klines: List[KLine], current_kline: KLine) -> bool:
        raise NotImplementedError("Must implement evaluate method.")

    def details(self) -> dict:
        return {}


class VolumeSpikeRule(BaseRule):
    def __init__(self, multiplier: float = 5.0):
        self.multiplier = multiplier
        self.rule_executed = False

        self.current_volume = 0
        self.volume_threshold = 0

    def evaluate(self, recent_klines: List[KLine], current_kline: KLine) -> bool:
        volumes = np.array([float(k.volume) for k in recent_klines])
        self.volume_threshold = volumes.mean() + self.multiplier * volumes.std()
        self.current_volume = float(current_kline.volume)
        self.rule_executed = self.current_volume > self.volume_threshold
        return self.rule_executed

    def details(self):
        return {'current_volume': self.current_volume, 'volume_threshold': self.volume_threshold}


class TradesSpikeRule(BaseRule):
    def __init__(self, multiplier: float = 4.0):
        self.multiplier = multiplier
        self.rule_executed = False

        self.current_trades = 0
        self.trades_threshold = 0

    def evaluate(self, recent_klines: List[KLine], current_kline: KLine) -> bool:
        trades = np.array([k.number_of_trades for k in recent_klines])
        self.trades_threshold = trades.mean() + self.multiplier * trades.std()
        self.current_trades = current_kline.number_of_trades
        self.rule_executed = self.current_trades > self.trades_threshold
        return self.rule_executed

    def details(self):
        return {'current_trades': self.current_trades, 'trades_threshold': self.trades_threshold}


class ImbalanceRule(BaseRule):
    def __init__(self, upper: float = 0.8, lower: float = 0.2):
        self.upper = upper
        self.lower = lower
        self.rule_executed = False

        self.current_buy_ratio = 0.0

    def evaluate(self, recent_klines: List[KLine], current_kline: KLine) -> bool:
        self.current_buy_ratio = (
            current_kline.taker_buy_quote_asset_volume / current_kline.quote_asset_volume
            if current_kline.quote_asset_volume > 0 else 0.5  # neutral if no volume
        )

        self.rule_executed = self.current_buy_ratio > self.upper or self.current_buy_ratio < self.lower
        return self.rule_executed

    def details(self):
        return {
            'buy_ratio': round(self.current_buy_ratio, 2)
        }

class PriceSpikeRule(BaseRule):
    def __init__(self, threshold: float = 0.01):
        self.threshold = threshold
        self.rule_executed = False

        self.current_high_price = 0.0
        self.price_change_percent = 0.0

    def evaluate(self, recent_klines: List[KLine], current_kline: KLine) -> bool:
        recent_closes = np.array([float(k.close_price) for k in recent_klines])
        avg_recent_close = recent_closes.mean()

        self.current_high_price = float(current_kline.high_price)

        self.price_change_percent = (self.current_high_price - avg_recent_close) / avg_recent_close
        self.rule_executed = abs(self.price_change_percent) >= self.threshold

        return self.rule_executed

    def details(self):
        return {
            'price_change_percent': round(self.price_change_percent * 100, 2),
            'current_high_price': self.current_high_price,
        }


class RuleEngine:
    def __init__(self, producer: KafkaProducer, buffer: KLinesBuffer, rules: List[BaseRule] = None):
        self.producer = producer
        self.buffer = buffer
        self.logger = setup_logging()
        self.rules = rules or [
            VolumeSpikeRule(),
            TradesSpikeRule(),
            ImbalanceRule(),
            PriceSpikeRule()
        ]

    def process_kline(self, kline: KLine):
        if len(self.buffer) < 20:
            return

        recent_klines = self.buffer.get_last(20)
        conditions_met = []
        details = {}

        for rule in self.rules:
            if rule.evaluate(recent_klines, kline):
                conditions_met.append(rule.__class__.__name__)
            details.update(rule.details())

        produced_combined = False
        if 'PriceSpikeRule' in conditions_met and len(conditions_met) >= 2:
            whale_alert = {
                'type': 'whale_activity_detected',
                'symbol': kline.symbol,
                'volume': kline.volume,
                'trades': kline.number_of_trades,
                'buy_ratio': details.get('buy_ratio', 0),
                'conditions_met': conditions_met,
                'interval': kline.interval,
                'timestamp': kline.close_time.strftime("%Y-%m-%d %H:%M:%S"),
                'current_high_price': details['current_high_price'],
                'price_change_percent': details['price_change_percent'],
                'close_price': kline.close_price,
            }
            self.producer.produce(whale_alert)
            self.logger.info(f"Produced {whale_alert}")

        if 'VolumeSpikeRule' in conditions_met and not produced_combined:
            volume_alert = {
                'type': 'volume_spike',
                'symbol': kline.symbol,
                'current_volume': details['current_volume'],
                'volume_threshold': details['volume_threshold'],
                'interval': kline.interval,
                'close_price': kline.close_price,
                'timestamp': kline.close_time.strftime("%Y-%m-%d %H:%M:%S"),
            }
            self.producer.produce(volume_alert)
            self.logger.info(f"Produced {volume_alert}")

        if 'TradesSpikeRule' in conditions_met and not produced_combined:
            trades_spike_alert = {
                'type': 'trades_spike',
                'symbol': kline.symbol,
                'current_trades': details['current_trades'],
                'trades_threshold': details['trades_threshold'],
                'interval': kline.interval,
                'close_price': kline.close_price,
                'timestamp': kline.close_time.strftime("%Y-%m-%d %H:%M:%S")
            }
            self.producer.produce(trades_spike_alert)
            self.logger.info(f"Produced {trades_spike_alert}")

        if 'PriceSpikeRule' in conditions_met and not produced_combined:
            price_spike_alert = {
                'type': 'price_spike',
                'symbol': kline.symbol,
                'current_high_price': details['current_high_price'],
                'price_change_percent': details['price_change_percent'],
                'interval': kline.interval,
                'close_price': kline.close_price,
                'timestamp': kline.close_time.strftime("%Y-%m-%d %H:%M:%S")
            }
            self.producer.produce(price_spike_alert)
            self.logger.info(f"Produced {price_spike_alert}")