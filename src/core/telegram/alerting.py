from abc import ABC, abstractmethod
from telegram.helpers import escape

class BaseAlertFormatter(ABC):
    @abstractmethod
    def format(self, alert: dict) -> str:
        pass

class TradesSpikeFormatter(BaseAlertFormatter):
    def format(self, alert: dict) -> str:
        return (
            f"🔔 <b>Trades Spike Detected!</b>\n"
            f"Symbol: <b>{alert['symbol']}</b>\n"
            f"Interval: <b>{alert['interval']}</b>\n"
            f"Trades (Threshold): <b>{alert['current_trades']} ({alert['trades_threshold']:.1f})</b>\n"
            f"Price: <b>{alert['close_price']}</b>"
        )

class PriceSpikeFormatter(BaseAlertFormatter):
    def format(self, alert: dict) -> str:
        return (
            f"🚨 <b>Price Spike Detected!</b>\n"
            f"Symbol: <b>{alert['symbol']}</b>\n"
            f"Interval: <b>{alert['interval']}</b>\n"
            f"High Price (Change %): <b>{alert['current_high_price']} ({alert['price_change_percent']}%)</b>\n"
            f"Price: <b>{alert['close_price']}</b>"
        )

class ImbalanceFormatter(BaseAlertFormatter):
    def format(self, alert: dict) -> str:
        imbalance = (
            "🔴 Sell" if alert['buy_ratio'] < 0.2 else
            "🟢 Buy" if alert['buy_ratio'] > 0.8 else
            "⚪ Neutral"
        )
        return (
            f"⚖️ <b>Imbalance Detected!</b>\n"
            f"Symbol: <b>{alert['symbol']}</b>\n"
            f"Interval: <b>{alert['interval']}</b>\n"
            f"Buy Ratio: <b>{alert['buy_ratio']}</b> ({imbalance})\n"
            f"Price: <b>{alert['close_price']}</b>"
        )

class VolumeSpikeFormatter(BaseAlertFormatter):
    def format(self, alert: dict) -> str:
        return (
            f"⚠️ <b>Volume Spike Detected!</b>\n"
            f"Symbol: <b>{alert['symbol']}</b>\n"
            f"Interval: <b>{alert['interval']}</b>\n"
            f"Volume (Threshold): <b>{alert['current_volume']} ({alert['volume_threshold']:.2f})</b>\n"
            f"Price: <b>{alert['close_price']}</b>"
        )

class WhaleActivityFormatter(BaseAlertFormatter):
    def format(self, alert: dict) -> str:
        imbalance = (
            "🔴 Sell" if alert['buy_ratio'] < 0.2 else
            "🟢 Buy" if alert['buy_ratio'] > 0.8 else
            "⚪ Neutral"
        )
        conditions = ', '.join(alert.get('conditions_met', []))
        return (
            f"🐳 <b>Whale Activity Detected!</b>\n"
            f"Symbol: <b>{alert['symbol']}</b>\n"
            f"Interval: <b>{alert['interval']}</b>\n"
            f"Volume: <b>{alert['volume']}</b>\n"
            f"Trades: <b>{alert['trades']}</b>\n"
            f"Buy Ratio: <b>{alert['buy_ratio']}</b> ({imbalance})\n"
            f"Conditions Met: <b>{conditions}</b>\n"
            f"Price: <b>{alert['close_price']}</b>"
        )

class RSIPredictionFormatter(BaseAlertFormatter):
    def format(self, alert: dict) -> str:
        direction = "📉 <b>RSI Oversold</b>" if alert['type'] == 'rsi_oversold' else "📈 <b>RSI Overbought</b>"
        return (
            f"{direction}\n"
            f"Symbol: <b>{alert['symbol']}</b>\n"
            f"RSI: <b>{alert['rsi']}</b>\n"
            f"Price: <b>{alert['close_price']}</b>"
        )

class MACDCrossoverFormatter(BaseAlertFormatter):
    def format(self, alert: dict) -> str:
        direction = "🚀 <b>MACD Bullish Crossover</b>" if alert['type'] == 'macd_bullish_crossover' else "🐻 <b>MACD Bearish Crossover</b>"
        return (
            f"{direction}\n"
            f"Symbol: <b>{alert['symbol']}</b>\n"
            f"Price: <b>{alert['close_price']}</b>"
        )

class MLPredictionFormatter(BaseAlertFormatter):
    def format(self, alert: dict) -> str:
        prediction = '🟢 <b>UP</b>' if alert['prediction'] == 1 else '🔴 <b>DOWN</b>'
        confidence = f"{alert['confidence'] * 100:.1f}%"
        return (
            f"📊 <b>ML Prediction</b>\n"
            f"<b>Pair:</b> <code>{alert['symbol'].upper()}</code>\n"
            f"<b>Interval:</b> <code>{alert['interval']}</code>\n"
            f"<b>Next candle:</b> {prediction}\n"
            f"<b>Confidence:</b> <code>{confidence}</code>\n"
        )

class ImageAlertFormatter(BaseAlertFormatter):
    def format(self, alert: dict) -> str:
        return (
            f"📷 <b>Trading Chart</b>\n"
            f"Symbol: <b>{alert['symbol']}</b>"
        )


class LLMRequestAlertFormatter(BaseAlertFormatter):
    def format(self, alert: dict) -> str:
        return (
            f"📷 <b>Request prompt:</b>\n"
            f"{alert['text']}\n"
        )

class DefaultAlertFormatter(BaseAlertFormatter):
    def format(self, alert: dict) -> str:
        return f"ℹ️ Alert received: <code>{str(alert)}</code>"

class AlertFormatterRegistry:
    def __init__(self):
        self.formatters = {
            'volume_spike': VolumeSpikeFormatter(),
            'trades_spike': TradesSpikeFormatter(),
            'price_spike': PriceSpikeFormatter(),
            'imbalance_detected': ImbalanceFormatter(),
            'whale_activity_detected': WhaleActivityFormatter(),
            'rsi_oversold': RSIPredictionFormatter(),
            'rsi_overbought': RSIPredictionFormatter(),
            'macd_bullish_crossover': MACDCrossoverFormatter(),
            'macd_bearish_crossover': MACDCrossoverFormatter(),
            'ml_prediction': MLPredictionFormatter(),
            'image': ImageAlertFormatter(),
            'llm_request': LLMRequestAlertFormatter(),
        }

    def format(self, alert: dict) -> str:
        formatter = self.formatters.get(alert.get('type'), DefaultAlertFormatter())
        return formatter.format(alert)

if __name__ == '__main__':
    test_alert = {
        'type': 'ml_prediction',
        'symbol': 'BTCUSDT',
        'interval': '15m',
        'prediction': 1,
        'confidence': 0.654,
        'threshold': 0.5
    }
    formatter_registry = AlertFormatterRegistry()
    print(formatter_registry.format(test_alert))
