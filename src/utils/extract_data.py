import datetime
import requests

import pandas as pd

from typing import Optional, Literal
from src.binance_client import get_client
from src.utils.misc import is_valid_iso_datetime
from src.config.base import COINMARKETCAP_API


def get_klines_df(symbol: str, interval: str, limit: int, end_time: Optional[str] = None,
                  client_name: str = "um_futures", tz: Literal["utc", "local"] = "local") -> pd.DataFrame:
    """
    Get klines (candlestick) data from Binance.

    Parameters:
    -----------
    symbol : str
        Trading pair symbol (e.g., "BTCUSDT")
    interval : str
        Kline interval (e.g., "1m", "15m", "1h", "4h", "1d")
    limit : int
        Number of klines to retrieve
    end_time : Optional[str]
        End time for the query in ISO format
    client_name : str
        Name of the client to use
    tz : Literal["utc", "local"]
        Timezone for datetime columns, either "utc" or "local"

    Returns:
    --------
    pd.DataFrame
        containing the klines data
    """
    MAX_API_LIMIT = 1000
    COLUMN_NAMES = [
        'open_time', 'open_price', 'high_price', 'low_price', 'close_price', 'volume',
        'close_time', 'quote_asset_volume', 'number_of_trades',
        'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'unknown'
    ]

    client = get_client(client_name)

    def fetch_klines(end_time: Optional[int] = None) -> list:
        kwargs = {"limit": min(limit, MAX_API_LIMIT)}
        if end_time:
            kwargs['endTime'] = end_time
        return client.klines(symbol=symbol, interval=interval, **kwargs)

    def process_klines(klines: list) -> pd.DataFrame:
        df = pd.DataFrame(klines, columns=COLUMN_NAMES)
        if df.empty:
            return df

        # Convert timestamp columns to datetime
        for col in ['open_time', 'close_time']:
            df[col] = pd.to_datetime(df[col], unit='ms', utc=True)
            
            # Convert to local timezone if requested
            if tz == "local":
                df[col] = df[col].dt.tz_convert(datetime.datetime.now().astimezone().tzinfo)
                # Remove timezone info while preserving local time
                df[col] = df[col].dt.tz_localize(None)

        num_cols = [col for col in df.columns if col not in ["open_time", "close_time", "unknown"]]
        df[num_cols] = df[num_cols].apply(pd.to_numeric, errors='coerce')

        df['number_of_trades'] = df['number_of_trades'].astype(int)
        df.drop('unknown', axis=1, inplace=True)
        return df

    all_klines = []
    remaining_limit = limit
    current_end_time = end_time

    while remaining_limit > 0:
        if isinstance(current_end_time, str) and is_valid_iso_datetime(current_end_time):
            current_end_time = int(datetime.datetime.fromisoformat(current_end_time).timestamp() * 1000)

        batch = fetch_klines(current_end_time)
        if not batch:
            break

        all_klines.extend(batch)
        remaining_limit -= len(batch)

        if len(batch) < MAX_API_LIMIT:
            break

        current_end_time = batch[0][0] - 1  # Use the oldest timestamp as the new end_time

    df = process_klines(all_klines)
    df.sort_values(by='open_time', inplace=True)
    return df[:limit]  # Ensure we don't return more than the requested limit


def get_orders_df(symbol: str = None, client_name: str = "um_futures", drop_columns: list = []) -> pd.DataFrame:
    column_mapping = {
        'orderId': 'order_id',
        'symbol': 'symbol',
        'status': 'status',
        'clientOrderId': 'client_order_id',
        'price': 'price',
        'avgPrice': 'avg_price',
        'origQty': 'orig_qty',
        'executedQty': 'executed_qty',
        'cumQuote': 'cum_quote',
        'timeInForce': 'time_in_force',
        'type': 'type',
        'reduceOnly': 'reduce_only',
        'closePosition': 'close_position',
        'side': 'side',
        'positionSide': 'position_side',
        'stopPrice': 'stop_price',
        'workingType': 'working_type',
        'priceProtect': 'price_protect',
        'origType': 'orig_type',
        'priceMatch': 'price_match',
        'selfTradePreventionMode': 'self_trade_prevention_mode',
        'goodTillDate': 'good_till_date',
        'time': 'time',
        'updateTime': 'update_time'
    }
    kwargs = {}
    if symbol:
        kwargs = {"symbol": symbol}

    client = get_client(client_name)
    df = pd.DataFrame([x for x in client.get_orders(**kwargs)])

    if not df.empty:
        df = df.rename(columns=column_mapping)
        df['time'] = df['time'].apply(lambda x: datetime.datetime.fromtimestamp(x / 1000))
        df['update_time'] = df['update_time'].apply(lambda x: datetime.datetime.fromtimestamp(x / 1000))

        num_cols = ['price', 'avg_price', 'orig_qty', 'executed_qty', 'cum_quote', 'stop_price']
        df[num_cols] = df[num_cols].apply(pd.to_numeric, errors='coerce')

        df.drop(columns=drop_columns, axis=1, inplace=True)
    else:
        df = pd.DataFrame(columns=column_mapping.values())

    return df


def get_open_positions_df(symbol: str = None, client_name: str = "um_futures", drop_columns: list = []) -> pd.DataFrame:
    kwargs = {}
    if symbol:
        kwargs = {"symbol": symbol}

    client = get_client(client_name)
    df = pd.DataFrame([x for x in client.get_position_risk(**kwargs)])

    if not df.empty:
        df['updateTime'] = df['updateTime'].apply(lambda x: datetime.datetime.fromtimestamp(x / 1000))

        df.drop(columns=drop_columns, axis=1, inplace=True)

    return df


def get_position_history_df(symbol: str = None, client_name: str = "um_futures") -> pd.DataFrame:
    column_mapping = {
        'symbol': 'symbol',
        'id': 'id',
        'order_id': 'order_id',
        'side': 'side',
        'price': 'price',
        'qty': 'qty',
        'realizedPnl': 'realized_pnl',
        'quoteQty': 'quote_qty',
        'commission': 'commission',
        'commissionAsset': 'commission_asset',
        'time': 'time',
        'positionSide': 'position_side',
        'maker': 'maker',
        'buyer': 'buyer',
    }
    kwargs = {}
    if symbol:
        kwargs = {"symbol": symbol}

    client = get_client(client_name)
    df = pd.DataFrame([x for x in client.get_account_trades(**kwargs)])

    if not df.empty:
        df = df.rename(columns=column_mapping)
        df['time'] = df['time'].apply(lambda x: datetime.datetime.fromtimestamp(x / 1000))

        # Separate BUY and SELL orders
        buys = df[df['side'] == 'BUY'].copy()
        sells = df[df['side'] == 'SELL'].copy()
        
        # Rename columns for clarity
        buys = buys.rename(columns={
            'time': 'position_open_time',
            'price': 'entry_price',
            'quote_qty': 'quantity_usd'
        })
        sells = sells.rename(columns={
            'time': 'position_close_time',
            'price': 'exit_price',
            'realized_pnl': 'pnl'
        })
        
        # Merge BUY and SELL orders
        merged = pd.merge_asof(
            buys.sort_values('position_open_time'),
            sells.sort_values('position_close_time'),
            by=['symbol', 'position_side', 'qty'],
            left_on='position_open_time',
            right_on='position_close_time',
            direction='forward'
        )
        
        # Select and rename relevant columns
        result = merged[[
            'symbol', 'qty', 'entry_price', 'exit_price', 'position_side', 'quantity_usd', 
            'pnl', 'position_open_time', 'position_close_time',
        ]].rename(columns={'qty': 'quantity'})

        num_cols = ['quantity', 'entry_price', 'exit_price', 'quantity_usd', 'pnl']
        result[num_cols] = result[num_cols].apply(pd.to_numeric, errors='coerce')
        
        # Filter out unmatched positions
        df = result.dropna(subset=['position_close_time'])
        
    return df


def get_current_price(symbol: str, client_name: str = "um_futures") -> pd.DataFrame:
    client = get_client(client_name)
    ticker = client.ticker_price(symbol=symbol)
    if ticker:
        ticker['price'] = float(ticker['price'])
        ticker['time'] = datetime.datetime.fromtimestamp(ticker['time'] / 1000).strftime('%Y-%m-%d %H:%M:%S')
    return ticker


def get_balance_df(
        asset: str,
        client_name: str = "um_futures",
        drop_cols: tuple = ("account_alias", "cross_wallet_balance", "cross_un_pnl",
                           "max_withdraw_amount", "margin_available", "update_time")
) -> pd.DataFrame:
    column_mapping = {
        "accountAlias": "account_alias",
        "asset": "asset",
        "balance": "balance",
        "crossWalletBalance": "cross_wallet_balance",
        "crossUnPnl": "cross_un_pnl",
        "availableBalance": "available_balance",
        "maxWithdrawAmount": "max_withdraw_amount",
        "marginAvailable": "margin_available",
        "updateTime": "update_time"
    }

    client = get_client(client_name)
    df = pd.DataFrame([x for x in client.balance()])

    if not df.empty:
        df = df.rename(columns=column_mapping)
        df['update_time'] = df['update_time'].apply(lambda x: datetime.datetime.fromtimestamp(x / 1000))

        num_cols = ['balance', 'cross_wallet_balance', 'cross_un_pnl', 'available_balance', 'max_withdraw_amount']
        df[num_cols] = df[num_cols].apply(pd.to_numeric, errors='coerce')

        if asset:
            df = df[df['asset'] == asset]

    if drop_cols:
        df.drop(drop_cols, axis=1, inplace=True)

    return df


def get_fear_greed_index():
    headers = {'Accepts': 'application/json', 'X-CMC_PRO_API_KEY': COINMARKETCAP_API["KEY"]}

    latest_resp = requests.get(COINMARKETCAP_API["FEAR_AND_GREED_LATEST_URL"], headers=headers)
    latest_resp.raise_for_status()
    latest_data = latest_resp.json()['data']

    historical_resp = requests.get(COINMARKETCAP_API["FEAR_AND_GREED_HISTORICAL_URL"], headers=headers, params={'limit': '35'})
    historical_resp.raise_for_status()
    historical_data = historical_resp.json()['data']

    def find_by_date(target_date):
        for item in historical_data:
            item_date = datetime.datetime.fromtimestamp(int(item['timestamp']), tz=datetime.timezone.utc).date()
            if item_date == target_date:
                return [item['value'], item['value_classification'].lower()]
        return None

    today = datetime.datetime.now(datetime.timezone.utc).date()

    return {
        "today": [latest_data['value'], latest_data['value_classification'].lower()],
        "yesterday": find_by_date(today - datetime.timedelta(days=1)),
        "last week": find_by_date(today - datetime.timedelta(days=7)),
        "last month": find_by_date(today - datetime.timedelta(days=30))
    }


if __name__ == '__main__':
    print(get_fear_greed_index())
