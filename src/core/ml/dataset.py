import pandas as pd

from src.utils.extract_data import get_klines_df
from src.core.technical_analysis.indicators import add_indicators


def add_dt_features(df: pd.DataFrame, dt_column: str = "open_time", inplace: bool = False) -> pd.DataFrame | None:
    if not inplace:
        df = df.copy()

    df['year'] = df[dt_column].dt.year
    df['month'] = df[dt_column].dt.month
    df['day'] = df[dt_column].dt.day
    df['hour'] = df[dt_column].dt.hour
    df['minute'] = df[dt_column].dt.minute
    df['day_of_week'] = df[dt_column].dt.dayofweek

    if inplace:
        return None
    else:
        return df

def add_lag_features(df: pd.DataFrame, lag_num: int = 3, lag_cols: list = [], inplace: bool = False) -> pd.DataFrame | None:
    if not inplace:
        df = df.copy()

    lags = range(1, lag_num + 1)
    for col in lag_cols:
        for lag in lags:
            df[f'{col}_lag_{lag}'] = df[col].shift(lag)

    if inplace:
        return None
    else:
        return df


def prepare_training_dataset(ticker: str, interval: str = "15m", limit: int = 1000,
                             indicators_config: dict = None, dt_features: bool = False, lag_features: dict = None) -> pd.DataFrame:
    df = get_klines_df(symbol=ticker, interval=interval, limit=limit)

    if indicators_config:
        df = add_indicators(df, indicators_config)

    if dt_features:
        df = add_dt_features(df)

    if lag_features:
        df = add_lag_features(df, lag_num=lag_features['lag_num'], lag_cols=lag_features['lag_cols'])

    return df


def finalize_training_dataset(df: pd.DataFrame, inplace: bool = False) -> pd.DataFrame | None:
    if not inplace:
        df = df.copy()

    drop_columns = ['close_time', 'close_price', 'high_price', 'low_price', 'open_price', 'year', 'month', 'day',
                    'ichimoku_senkou_span_a', 'ichimoku_senkou_span_b', 'ichimoku_chikou_span']

    df['target'] = (df['close_price'].shift(-1) > df['close_price']).astype(int)

    df = df.drop(drop_columns, axis=1)
    df = df.dropna().reset_index(drop=True)
    df = df.set_index('open_time')

    if inplace:
        return None
    else:
        return df
