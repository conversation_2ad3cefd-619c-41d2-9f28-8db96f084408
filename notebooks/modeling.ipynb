#%%
%load_ext autoreload
%autoreload 2
#%%
import pandas as pd
import numpy as np
import datetime

from src.binance_client import get_client

from src.core.technical_analysis.data_structures.kline import KLine
from src.utils.extract_data import get_klines_df
from src.core.technical_analysis.indicators import add_indicators
from src.core.ml.dataset import add_lag_features, add_dt_features
#%%

#%%
ticker: str = "SUIUSDT"
# client_name: str = "um_futures"
#%%
INDICATORS_CONFIG = {
    'sma': [{'window': 14}, {'window': 50}, {'window': 99}],
    'ema': [{'span': 8}, {'span': 20}, {'span': 50}],
    'rsi': True, # Use defaults (window=14)
    'macd': True, # Use defaults (12, 26, 9)
    'bollinger': True,
    'price_channel': True,
    'atr': {'window': 14},
    # <PERSON>ltner will internally call EMA(20) and ATR(10) by its defaults
    # unless specified otherwise here AND in the calculate_keltner function call parameters
    'keltner': {'ema_window': 20, 'atr_window': 10, 'multiplier': 2.0},
    'stochastic': True,
    'obv': True,
    'cci': True,
    'williams_r': True,
    'ichimoku': {"include_span": True},
    'vwap': True,
}
#%%
df = get_klines_df(symbol=ticker, interval="15m", limit=15000)

df = add_indicators(df, INDICATORS_CONFIG)
df = add_dt_features(df)
df = add_lag_features(df, lag_num=3, lag_cols=['close_price', 'volume', 'rsi', 'macd', 'ema_20', 'sma_50'])
#%%
drop_columns = ['close_time', 'close_price', 'high_price', 'low_price', 'open_price', 'year', 'month', 'day',
                'ichimoku_senkou_span_a', 'ichimoku_senkou_span_b', 'ichimoku_chikou_span']

df['target'] = (df['close_price'].shift(-1) > df['close_price']).astype(int)

df = df.drop(drop_columns, axis=1)
df = df.dropna().reset_index(drop=True)
df = df.set_index('open_time')
#%%
train_size = int(len(df) * 0.9)
X, y = df.drop(columns='target'), df['target']
X_train, X_test, y_train, y_test = (
    X.iloc[:train_size], X.iloc[train_size:], y.iloc[:train_size], y.iloc[train_size:]
)
#%% md
## Modeling
#%%
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import (
    classification_report, precision_recall_curve,
    f1_score, auc, roc_auc_score, confusion_matrix
)
from sklearn.calibration import calibration_curve
import matplotlib.pyplot as plt
#%%
model = RandomForestClassifier(n_estimators=100, random_state=42).fit(X_train, y_train)
best_threshold = 0.5  # default value

y_pred_proba = model.predict_proba(X_test)[:, 1]
y_pred = (y_pred_proba >= best_threshold).astype(int)
precision, recall, thresholds = precision_recall_curve(y_test, y_pred_proba)
roc_auc = roc_auc_score(y_test, y_pred_proba)
pr_auc = auc(recall, precision)
cm = confusion_matrix(y_test, y_pred)

importances = pd.Series(model.feature_importances_, index=X.columns).sort_values(ascending=False)

print(classification_report(y_test, y_pred, zero_division=1))
print(f"\nROC AUC: {roc_auc:.4f}")
print(f"\nPR AUC: {pr_auc:.4f}")
print(f"\nConfusion Matrix:\n{cm}")
print("\nTop 10 Features:")
print(importances.head(10))
#%%
# Compute calibration curve
prob_true, prob_pred = calibration_curve(y_test, y_pred_proba, n_bins=50)

# Plot calibration curve
plt.figure(figsize=(8, 6))
plt.plot(prob_pred, prob_true, marker='o', linewidth=1, label='Random Forest')
plt.plot([0, 1], [0, 1], linestyle='--', label='Perfect Calibration')
plt.title('Calibration Curve')
plt.xlabel('Predicted Probability')
plt.ylabel('True Probability')
plt.legend()
plt.grid()
plt.show()
#%%

#%%
df.columns
#%%
df.columns
#%%

#%% md

#%%

#%%
def add_lagged_features(df, lags=[1, 2]):
    """
    Add lagged features to the dataframe.
    
    :param df: Input dataframe
    :param lags: List of lag values to create features for
    :return: Dataframe with added lagged features
    """
    features_to_lag = [
        'open_price', 'high_price', 'low_price', 'close_price',
        'volume', 'quote_asset_volume', 'number_of_trades',
        'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume',
        'sma_7', 'sma_25', 'sma_99', 'ema_7', 'ema_25', 'ema_99',
        'rsi', 'macd', 'macd_signal', 'macd_hist',
        'bb_middle', 'bb_upper', 'bb_lower',
        'price_channel_high', 'price_channel_low'
    ]
    
    for feature in features_to_lag:
        for lag in lags:
            df[f'{feature}_{lag}'] = df[feature].shift(lag)
    
    return df
#%%
df = get_klines_df(ticker, '1m', 50000)
df = add_indicators(df)
df = add_lagged_features(df, lags=[1, 2])

df['target'] = np.where(df['close_price'] > df['close_price'].shift(1), 1,0)
df['target'] = df['target'].shift(-1)
df = df.dropna().reset_index(drop=True)
#%%

#%%
print(df.shape)
df.head()
#%%
df.columns
#%%

#%%

#%%
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score
from lightgbm import LGBMClassifier
from lightgbm.callback import early_stopping
#%%

#%%
data = df.copy()

X = data.drop(['target', 'open_time', 'close_time'], axis=1)
y = data['target']
#%%
split_ratio = 0.9
split_idx = int(len(X) * split_ratio)

print(data.iloc[0].open_time, data.iloc[split_idx].open_time, data.iloc[-1].open_time)
#%%
X_train, X_test = X[:split_idx], X[split_idx:]
y_train, y_test = y[:split_idx], y[split_idx:]
#%%
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)
#%%
rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
rf_model.fit(X_train_scaled, y_train);
#%%
y_pred = rf_model.predict(X_test_scaled)

accuracy = accuracy_score(y_test, y_pred)
report = classification_report(y_test, y_pred)

print(f"Accuracy: {accuracy:.4f}")
print("\nClassification Report for the last fold:")
print(report)
#%%

#%%
params = {
    'objective': 'binary',
    'boosting_type': 'rf',
    'num_leaves': 31,
    'learning_rate': 0.05,
    'feature_fraction': 0.9,
    'n_estimators': 1000,
    'max_depth': -1,
    'n_jobs': -1,
    'random_state': 42
}

lgb_model = LGBMClassifier(**params)

lgb_model.fit(
    X_train_scaled, y_train,
    # eval_set=[(X_test_scaled, y_test)],
    # eval_metric='auc',
    # callbacks=[
    #     early_stopping(stopping_rounds=100, verbose=True)
    # ],
    # verbose=100  # Print evaluation every 100 iterations
)

print(f"Best iteration: {lgb_model.best_iteration_}")
#%%
y_pred = lgb_model.predict(X_test_scaled)

accuracy = accuracy_score(y_test, y_pred)
report = classification_report(y_test, y_pred)

print(f"Accuracy: {accuracy}")
print("Classification Report:")
print(report)
#%%
# Feature importance
# feature_importance = pd.DataFrame({'feature': X.columns, 'importance': model.feature_importances_})
# feature_importance = feature_importance.sort_values('importance', ascending=False)
# print("\nTop 10 Most Important Features:")
# print(feature_importance.head(10))
#%%

#%%

#%%
df1 = df.copy()
#%%
df1 = add_lagged_features(df1, lags=[1, 2])

df1['target'] = np.where(df1['close_price'] > df['close_price'].shift(1), 1, 0)
df1['target'] = df1['target'].shift(-1)

df1 = df1.dropna().reset_index(drop=True)

X = df1.drop(['target', 'open_time', 'close_time'], axis=1)
y = df1['target']
#%%
X
#%%

#%%

#%%

#%%

#%%
import torch
from torch import nn
import pytorch_lightning as pl
from pytorch_lightning.callbacks import EarlyStopping
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import classification_report, accuracy_score
from torch.utils.data import Dataset, DataLoader
#%%

#%%
# Load the data
data = df.copy()

# Convert 'open_time' to datetime and set it as index
data['open_time'] = pd.to_datetime(data['open_time'])
data.set_index('open_time', inplace=True)

# Separate features and target
features = data.drop(['target', 'close_time'], axis=1)
target = data['target']

#%%
# Scale the features
scaler = StandardScaler()
features_scaled = scaler.fit_transform(features)
#%%
# Function to create sequences
def create_sequences(data, targets, sequence_length):
    X, y = [], []
    for i in range(len(data) - sequence_length):
        X.append(data[i:(i + sequence_length)])
        y.append(targets[i + sequence_length])
    return np.array(X), np.array(y)
#%%
# Set sequence length
sequence_length = 10

# Create sequences
X, y = create_sequences(features, target.values, sequence_length)
#%%
X.shape
#%%
class FinancialDataset(Dataset):
    def __init__(self, X, y):
        self.X = torch.FloatTensor(X)
        self.y = torch.FloatTensor(y)

    def __len__(self):
        return len(self.y)

    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]
#%%
# Create PyTorch Lightning Module
class LSTMModel(pl.LightningModule):
    def __init__(self, input_size, hidden_size, num_layers, output_size):
        super().__init__()
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_size, output_size)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        lstm_out = lstm_out[:, -1, :]
        out = self.fc(lstm_out)
        out = self.sigmoid(out)
        return out

    def training_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self(x)
        loss = nn.BCELoss()(y_hat, y.unsqueeze(1))
        self.log('train_loss', loss)
        return loss

    def validation_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self(x)
        loss = nn.BCELoss()(y_hat, y.unsqueeze(1))
        self.log('val_loss', loss)
        return loss

    def configure_optimizers(self):
        return torch.optim.Adam(self.parameters(), lr=0.001)
#%%
# Set up TimeSeriesSplit
tscv = TimeSeriesSplit(n_splits=5)

# Initialize lists to store results
accuracies = []
reports = []

# Perform time series cross-validation
for fold, (train_index, val_index) in enumerate(tscv.split(X)):
    print(f"Fold {fold + 1}")

    # Split data
    X_train, X_val = X[train_index], X[val_index]
    y_train, y_val = y[train_index], y[val_index]

    # Create datasets and dataloaders
    train_dataset = FinancialDataset(X_train, y_train)
    val_dataset = FinancialDataset(X_val, y_val)
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=32)

    # Initialize model
    model = LSTMModel(input_size=X.shape[2], hidden_size=64, num_layers=2, output_size=1)

    # Set up trainer
    early_stop_callback = EarlyStopping(monitor='val_loss', patience=5)
    trainer = pl.Trainer(max_epochs=50, callbacks=[early_stop_callback])

    # Train model
    trainer.fit(model, train_loader, val_loader)

    # Make predictions
    model.eval()
    with torch.no_grad():
        y_pred = model(torch.FloatTensor(X_val)).numpy()
    y_pred = (y_pred > 0.5).astype(int).flatten()

    # Evaluate the model
    accuracy = accuracy_score(y_val, y_pred)
    report = classification_report(y_val, y_pred)
    
    accuracies.append(accuracy)
    reports.append(report)

    print(f"Accuracy: {accuracy:.4f}")
    print("Classification Report:")
    print(report)
    print("\n")
#%%
# Print average accuracy
print(f"Average Accuracy: {np.mean(accuracies):.4f}")

# Print the last classification report (for the most recent data)
print("\nClassification Report for the last fold:")
print(reports[-1])

# Feature importance (using permutation importance)
from sklearn.inspection import permutation_importance

# Reshape X_val to 2D for permutation_importance
X_val_2d = X_val.reshape(X_val.shape[0], -1)

def predict_fn(X):
    X_tensor = torch.FloatTensor(X.reshape(-1, sequence_length, features.shape[1]))
    with torch.no_grad():
        return model(X_tensor).numpy().flatten()

perm_importance = permutation_importance(predict_fn, X_val_2d, y_val, n_repeats=10, random_state=42)

feature_importance = pd.DataFrame({'feature': features.columns.repeat(sequence_length),
                                   'importance': perm_importance.importances_mean})
feature_importance = feature_importance.groupby('feature').mean().sort_values('importance', ascending=False)

print("\nTop 10 Most Important Features:")
print(feature_importance.head(10))
#%%

#%%

#%%

#%%
