#%%
%load_ext autoreload
%autoreload 2
#%%
import os
import shutil
import json
import datetime
import requests
import logging
import pandas as pd

from src.binance_client import get_server_time
from src.config import (IMAGES_PATH, OPENAI_API_HEADERS, INTERVAL_LIMIT_MAPPING, DROP_COLUMNS, DATA_PATH,
                        OPENAI_API_PAYLOAD, OPENAI_API_URL)
from src.core.futures.data_structures import Order, OpenPosition, ClosedPosition
from src.core.futures.trade.operations import handle_choice
from src.core.llm.utils import parse_response
from src.core.llm.data_structures.llm_response import LLMResponse
from src.core.technical_analysis.indicators import add_indicators
from src.core.technical_analysis.data_structures import KLine
from src.utils.extract_data import (get_klines_df, get_orders_df, get_open_positions_df, get_position_history_df,
                                    get_balance_df, get_current_price)
from src.utils.image_utils import concat_images, encode_image
from src.utils.db_utils import insert_records, fetch_last_n_records, is_db_exists
from src.misc.utils import parallel_execute
from src.utils.visualisation import calc_swings_and_plot

from tasks.apply_schema import apply_schema
#%%
logger = logging.getLogger(__name__)
logger.name = "llm_approach_v2.ipynb"
#%%
ticker, asset = "SUIUSDT", "USDT"
#%%

#%% md
## Collect data
#%%
def collect_data(ticker: str, asset: str, client_name: str = "um_futures"):
    """Collects data required for trading."""
    functions_with_kwargs = [
        (get_open_positions_df,
         dict(symbol=ticker, client_name=client_name, drop_columns=DROP_COLUMNS["open_positions_df"])),
        (get_position_history_df,
         dict(symbol=ticker, client_name=client_name, drop_columns=DROP_COLUMNS["position_history_df"])),
        (get_orders_df, dict(symbol=ticker, client_name=client_name, drop_columns=DROP_COLUMNS["open_orders_df"])),
        (get_klines_df, dict(symbol=ticker, interval="1m", limit=INTERVAL_LIMIT_MAPPING["1m"] + 100)),
        (get_klines_df, dict(symbol=ticker, interval="15m", limit=INTERVAL_LIMIT_MAPPING["15m"] + 100)),
        (get_klines_df, dict(symbol=ticker, interval="1h", limit=INTERVAL_LIMIT_MAPPING["1h"] + 100)),
        (get_klines_df, dict(symbol=ticker, interval="4h", limit=INTERVAL_LIMIT_MAPPING["4h"] + 100)),
        (get_klines_df, dict(symbol=ticker, interval="1d", limit=INTERVAL_LIMIT_MAPPING["1d"] + 100)),
        (get_balance_df, dict(asset=asset))
    ]
    results = parallel_execute(functions_with_kwargs)
    data_dfs = dict(zip([
        'open_positions_df', 'position_history_df', 'open_orders_df',
        'klines_1m_df', 'klines_15m_df', 'klines_1h_df', 'klines_4h_df',
        'klines_1d_df', 'balance_df'
    ], results))
    return data_dfs
#%%

#%% md
## Calculate features
#%%
def calculate_features(data_dfs):
    """Calculates technical indicators and adds them to the dataframes."""
    data_dfs['klines_1m_df'] = add_indicators(data_dfs['klines_1m_df'])
    data_dfs['klines_15m_df'] = add_indicators(data_dfs['klines_15m_df'])
    data_dfs['klines_1h_df'] = add_indicators(data_dfs['klines_1h_df'])
    return data_dfs
#%%

#%% md
## Save data to db
#%%
def save_data_to_db(ticker: str, data_dfs):
    """Saves the dataframes to the database."""
    functions_with_kwargs = [
        (insert_records, dict(data=[KLine(**item) for item in data_dfs['klines_1m_df'].to_dict(orient="records")],
                              table_name="klines_1m",
                              conflict_columns=("open_time", "close_time"),
                              db_name=ticker)),
        (insert_records, dict(data=[KLine(**item) for item in data_dfs['klines_15m_df'].to_dict(orient="records")],
                              table_name="klines_15m",
                              conflict_columns=("open_time", "close_time"),
                              db_name=ticker)),
        (insert_records, dict(data=[KLine(**item) for item in data_dfs['klines_1h_df'].to_dict(orient="records")],
                              table_name="klines_1h",
                              conflict_columns=("open_time", "close_time"),
                              db_name=ticker)),
        (insert_records, dict(data=[KLine(**item) for item in data_dfs['klines_4h_df'].to_dict(orient="records")],
                              table_name="klines_4h",
                              conflict_columns=("open_time", "close_time"),
                              db_name=ticker)),
        (insert_records, dict(data=[KLine(**item) for item in data_dfs['klines_1d_df'].to_dict(orient="records")],
                              table_name="klines_1d",
                              conflict_columns=("open_time", "close_time"),
                              db_name=ticker)),

        (insert_records, dict(data=[Order(**item) for item in data_dfs['open_orders_df'].to_dict(orient="records")],
                              table_name="orders",
                              conflict_columns=("order_id", "client_order_id"),
                              db_name=ticker)),
        (insert_records, dict(data=[OpenPosition(**item) for item in data_dfs['open_positions_df'].to_dict(orient="records")],
                              table_name="open_positions",
                              conflict_columns=("symbol", "position_side"),
                              db_name=ticker)),
        (insert_records, dict(data=[ClosedPosition(**item) for item in data_dfs['position_history_df'].to_dict(orient="records")],
                              table_name="closed_positions",
                              db_name=ticker)),
    ]
    parallel_execute(functions_with_kwargs)
#%%

#%% md
## Generate images
#%%
def generate_images(ticker: str, data_dfs):
    """Generates images for the LLM prompt."""
    shutil.rmtree(IMAGES_PATH / ticker, ignore_errors=True)
    functions_with_kwargs = [
        (calc_swings_and_plot, dict(df=data_dfs['klines_1m_df'], ticker=ticker, interval="1m")),
        (calc_swings_and_plot, dict(df=data_dfs['klines_15m_df'], ticker=ticker, interval="15m")),
        # (calc_swings_and_plot, dict(df=data_dfs['klines_1h_df'], ticker=ticker, interval="1h")),
    ]
    parallel_execute(functions_with_kwargs)
    image_files = sorted((IMAGES_PATH / ticker).glob("*.png"), key=lambda x: os.path.getctime(x))
    image_files = [img.as_posix() for img in image_files]
    image_path = IMAGES_PATH / ticker / "concated.png"
    concat_images(image_files, image_path)
    return image_path
#%%

#%% md
## Prepare prompt data
#%%
# def prepare_prompt_data(ticker: str, data_dfs: dict, image_path: str):
#     """
#     Prepares data for the LLM prompt.
# 
#     Args:
#         ticker (str): The ticker symbol of the asset.
#         data_dfs (dict): A dictionary containing dataframes collected earlier.
#         image_path (str): The path to the concatenated image file.
# 
#     Returns:
#         Tuple[str, str, str]: Returns the system message content, user message content, and base64 encoded image.
#     """
#     # Encode the image to base64 format for inclusion in the LLM prompt
#     base64_image = encode_image(image_path)
# 
#     # Prepare functions and their arguments to process dataframes into dataclass instances
#     functions_with_kwargs = [
#         # Convert open_positions_df to a list of OpenPosition dataclass instances
#         (df2dataclass, {
#             'df': data_dfs['open_positions_df'],
#             'dataclass': OpenPosition,
#             'drop_columns': []
#         }),
#         # Convert position_history_df to a list of ClosedPosition dataclass instances
#         (df2dataclass, {
#             'df': data_dfs['position_history_df'],
#             'dataclass': ClosedPosition,
#             'drop_columns': []
#         }),
#         # Convert open_orders_df to a list of Order dataclass instances
#         (df2dataclass, {
#             'df': data_dfs['open_orders_df'],
#             'dataclass': Order,
#             'drop_columns': []
#         }),
#         # Convert klines_1m_df to a list of KLine dataclass instances, dropping unnecessary columns
#         (df2dataclass, {
#             'df': data_dfs['klines_1m_df'],
#             'dataclass': KLine,
#             'drop_columns': []
#         }),
#         # Convert klines_15m_df to a list of KLine dataclass instances
#         (df2dataclass, {
#             'df': data_dfs['klines_15m_df'],
#             'dataclass': KLine,
#             'drop_columns': []
#         }),
#         # Convert klines_1h_df to a list of KLine dataclass instances
#         (df2dataclass, {
#             'df': data_dfs['klines_1h_df'],
#             'dataclass': KLine,
#             'drop_columns': []
#         }),
#         # Convert balance_df to a list of Balance dataclass instances
#         (df2dataclass, {
#             'df': data_dfs['balance_df'],
#             'dataclass': Balance,
#             'drop_columns': ["account_alias", "cross_wallet_balance", "cross_un_pnl",
#                              "max_withdraw_amount", "margin_available", "update_time"]
#         }),
#     ]
# 
#     # Execute the functions in parallel and collect the results
#     results = parallel_execute(functions_with_kwargs)
# 
#     # Unpack the results into variables for easier access
#     (open_positions, closed_positions, open_orders,
#      klines_1m, klines_15m, klines_1h, balance) = results
# 
#     # Process and format the data for the prompt
#     # Convert dataclass instances to dictionaries and format them as needed
#     open_positions = [x.dict() for x in open_positions][::-1]  # Reverse to get the newest first
#     closed_positions = [x.dict() for x in closed_positions][::-1]
#     open_orders = [x.dict() for x in open_orders][::-1]
#     klines_1m = [x.dict() for x in klines_1m][-10:][::-1]  # Last 10 entries
#     klines_15m = [x.dict() for x in klines_15m][-5:][::-1]  # Last 5 entries
#     klines_1h = [x.dict() for x in klines_1h][-5:][::-1]
#     balance = [x.dict() for x in balance]
# 
#     # Fetch previous LLM responses from the database
#     history_response_history = [
#         LLMResponse(*item).dict() for item in fetch_last_n_records(
#             table_name="llm_response_history",
#             n=10,
#             order_col="response_time",
#             db_name=ticker
#         )
#     ]
# 
#     # Get the current server time and price
#     server_time = get_server_time(to_str=True)
#     server_price = float(get_current_price(ticker)['price'])
# 
#     # Read the system and user prompt templates from files
#     with open(DATA_PATH / "prompts" / "system_message_content_v2.md", "rb") as f:
#         system_message_content = f.read().decode("utf-8")
# 
#     with open(DATA_PATH / "prompts" / "user_message_content_v2.md", "rb") as f:
#         user_message_content = f.read().decode("utf-8")
# 
#     # Format the user message content with the collected data
#     user_message_content = user_message_content.format(
#         ticker=ticker,
#         server_price=server_price,
#         server_time=server_time,
#         previous_responses=yaml_snippet(history_response_history),
#         closed_positions=yaml_snippet(closed_positions),
#         open_positions=yaml_snippet(open_positions),
#         open_orders=yaml_snippet(open_orders),
#         klines_1m=yaml_snippet(shorten_json_data(klines_1m)),
#         klines_15m=yaml_snippet(shorten_json_data(klines_15m)),
#         klines_1h=yaml_snippet(shorten_json_data(klines_1h)),
#         balance=yaml_snippet(balance[0]),
#     )
# 
#     # Return the formatted prompt components
#     return system_message_content, user_message_content, base64_image

def prepare_prompt_data(ticker: str, data_dfs: dict, image_path: str):
    """
    Prepares data for the LLM prompt.

    Args:
        ticker (str): The ticker symbol of the asset.
        data_dfs (dict): A dictionary containing dataframes collected earlier.
        image_path (str): The path to the concatenated image file.

    Returns:
        Tuple[str, str, str]: Returns the system message content, user message content, and base64 encoded image.
    """
    # Encode the image to base64 format for inclusion in the LLM prompt
    base64_image = encode_image(image_path)

    # Fetch previous LLM responses from the database
    history_response_history = pd.DataFrame([
        LLMResponse(*item).dict() for item in fetch_last_n_records(
            table_name="llm_response_history",
            n=5,
            order_col="response_time",
            db_name=ticker
        )
    ])

    # Get the current server time and price
    server_time = get_server_time(to_str=True)
    server_price = float(get_current_price(ticker)['price'])

    # Read the system and user prompt templates from files
    with open(DATA_PATH / "prompts" / "system_message_content_v2.md", "rb") as f:
        system_message_content = f.read().decode("utf-8")

    with open(DATA_PATH / "prompts" / "user_message_content_v2.md", "rb") as f:
        user_message_content = f.read().decode("utf-8")

    # Format the user message content with the collected data
    user_message_content = user_message_content.format(
        ticker=ticker,
        server_price=server_price,
        server_time=server_time,
        previous_responses=history_response_history.to_csv(index=False),
        positions_history=data_dfs['position_history_df'].to_csv(index=False),
        open_positions=data_dfs['open_positions_df'].to_csv(index=False),
        open_orders=data_dfs['open_orders_df'].to_csv(index=False),
        klines_1m=data_dfs['klines_1m_df'].tail(5).round(5).to_csv(index=False),
        klines_15m=data_dfs['klines_15m_df'].tail(5).round(5).to_csv(index=False),
        klines_1h=data_dfs['klines_1h_df'].tail(5).round(5).to_csv(index=False),
        balance=data_dfs['balance_df'].round(2).to_csv(index=False),
    )

    # Return the formatted prompt components
    return system_message_content, user_message_content, base64_image
#%%

#%% md
## Make api call
#%%
def make_api_call(system_message_content: str, user_message_content: str, base64_image: str):
    """Makes the API call to OpenAI."""
    payload = OPENAI_API_PAYLOAD.copy()
    payload['messages'][0]['content'] = system_message_content
    payload['messages'][1]['content'][0]['text'] = user_message_content
    payload['messages'][1]['content'][1]['image_url']['url'] = f"data:image/png;base64,{base64_image}"
    response = requests.post(OPENAI_API_URL, headers=OPENAI_API_HEADERS, json=payload).json()
    return response
#%%

#%% md
## Process response
#%%
def process_response(ticker: str, response):
    """Processes the LLM response and saves it to the database."""
    parsed_response = parse_response(response)
    try:
        choice, order = parsed_response['choice'], parsed_response['order']
    except KeyError:
        logger.error("Failed to parse response", extra={"llm_response": response['choices'][0]['message']['content']})
        return parsed_response, False
    parsed_response['response_time'] = datetime.datetime.fromtimestamp(response['created']).isoformat()
    parsed_response['order'] = json.dumps(order, indent=2)
    insert_records([LLMResponse(**parsed_response)],
                   "llm_response_history",
                   conflict_columns=("response_time",),
                   db_name=ticker)
    parsed_response['order'] = order
    return parsed_response, True
#%%

#%% md
## Execute choice
#%%
def execute_choice(choice, ticker, order, parsed_response, open_orders):
    """Executes the trading decision based on the LLM's choice."""
    return handle_choice(choice, ticker, order, parsed_response, open_orders)

#%%

#%% md
## Trade step
#%%
def trade_step(ticker: str = "SUIUSDC", asset: str = "USDC", client_name: str = "um_futures"):
    """Executes one step of the trading loop."""
    data_dfs = collect_data(ticker, asset, client_name)
    data_dfs = calculate_features(data_dfs)
    save_data_to_db(ticker, data_dfs)
    image_path = generate_images(ticker, data_dfs)
    system_message_content, user_message_content, base64_image = prepare_prompt_data(ticker, data_dfs, image_path)
    response = make_api_call(system_message_content, user_message_content, base64_image)
    parsed_response, success = process_response(ticker, response)
    if not success:
        return parsed_response, False
    choice = parsed_response['choice']
    order = parsed_response['order']
    open_orders = data_dfs['open_orders_df']
    print(parsed_response)
    exec_result = execute_choice(choice, ticker, order, parsed_response, open_orders)
    return parsed_response, exec_result
#%%

#%% md
## General pipeline
#%%
if not is_db_exists(ticker):
    apply_schema(ticker)
#%%
data_dfs = collect_data(ticker, asset)
data_dfs.keys()
#%%
data_dfs = calculate_features(data_dfs)
# data_dfs['klines_1m_df'].tail(2)
#%%
save_data_to_db(ticker, data_dfs)
#%%
image_path = generate_images(ticker, data_dfs)
image_path
#%%
system_message_content, user_message_content, base64_image = prepare_prompt_data(ticker, data_dfs, image_path)
#%%
response = make_api_call(system_message_content, user_message_content, base64_image)
#%%
parsed_response, success = process_response(ticker, response)
#%%
choice = parsed_response['choice']
order = parsed_response['order']
open_orders = data_dfs['open_orders_df']
print(json.dumps(parsed_response, indent=2))
#%%
print(system_message_content)
#%%
print(user_message_content)
#%%

#%%

#%%

#%%
