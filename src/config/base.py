import os
from pathlib import Path
from typing import Final

from dotenv import load_dotenv

load_dotenv()

# TODO: review and refactor config, all important pass from .env

if os.environ.get('TELEGRAM_BOT_TOKEN') and '\\x3a' in os.environ.get('TELEGRAM_BOT_TOKEN'):
    # Replace the escaped colon with a literal colon
    os.environ['TELEGRAM_BOT_TOKEN'] = os.environ['TELEGRAM_BOT_TOKEN'].replace('\\x3a', ':')

TELEGRAM_BOT_TOKEN: Final[str] = os.environ.get('TELEGRAM_BOT_TOKEN')
TELEGRAM_CHAT_ID = 282201952

KAFKA_TOPIC = 'signals'
KAFKA_SERVERS = os.environ.get('KAFKA_SERVERS', 'localhost:29092')

APP_NAME: Final[str] = os.environ.get('APP_NAME', "crypto-42")
ENV: Final[str] = os.environ.get("ENV", "dev")
ROOT_PATH: Final[Path] = Path(__file__).parent.parent.parent
DATA_PATH: Final[Path] = ROOT_PATH / "data"

IMAGES_PATH: Final[Path] = DATA_PATH / "images"

DB_SCHEMA_PATH: Final[Path] = DATA_PATH / "schema.sql"
DB_PATH: Final[Path] = DATA_PATH / "sqlite"

OPENAI_API_KEY: Final[str | None] = os.environ.get('OPENAI_API_KEY')
OPENAI_API_URL: Final[str] = os.environ.get('OPENAI_API_URL', "https://api.openai.com/v1/chat/completions")
OPENAI_MODEL: Final[str] = os.environ.get('OPENAI_MODEL', "gpt-4o-mini")
OPENAI_MAX_TOKENS: Final[int] = int(os.environ.get('OPENAI_MAX_TOKENS', 1000))

OPENAI_API_HEADERS_BASE: Final[dict[str, str]] = {
    "Content-Type": "application/json",
}

BINANCE_API_KEY = os.environ.get('BINANCE_API_KEY')
BINANCE_SECRET_KEY = os.environ.get('BINANCE_SECRET_KEY')

COINMARKETCAP_API = {
    "KEY": os.environ.get('COINMARKETCAP_API_KEY'),
    "FEAR_AND_GREED_LATEST_URL": "https://pro-api.coinmarketcap.com/v3/fear-and-greed/latest",
    "FEAR_AND_GREED_HISTORICAL_URL": "https://pro-api.coinmarketcap.com/v3/fear-and-greed/historical",
}

MINIO_SERVER = os.environ.get('MINIO_SERVER', 'localhost:9000')
MINIO_ENDPOINT_URL =  f'http://{MINIO_SERVER}'
MINIO_CONFIG: Final[dict[str, str]] = {
    "endpoint_url": MINIO_ENDPOINT_URL,
    "aws_access_key_id": 'minioadmin',
    "aws_secret_access_key": 'minioadmin'
}
MINIO_BUCKET: Final[str] = "images"

## ------------------------------------------------------------------------------------------------ ##
## ------------------------------------------------------------------------------------------------ ##

INDICATORS_CONFIG: Final[dict[str, str | list[dict] | dict]] = {
    'sma': [{'window': 14}, {'window': 50}, {'window': 99}],
    'ema': [{'span': 8}, {'span': 20}, {'span': 50}],
    'rsi': True, # Use defaults (window=14)
    'macd': True, # Use defaults (12, 26, 9)
    'bollinger': True,
    'price_channel': True,
    'atr': {'window': 14},
    # Keltner will internally call EMA(20) and ATR(10) by its defaults
    # unless specified otherwise here AND in the calculate_keltner function call parameters
    'keltner': {'ema_window': 20, 'atr_window': 10, 'multiplier': 2.0},
    'stochastic': True,
    'obv': True,
    'cci': True,
    'williams_r': True,
    'ichimoku': {"include_span": True},
    'vwap': True,
}

# INTERVAL_LIMIT_MAPPING: Final[dict[str, int]] = {
#     "1m": 60*4,     # 4 hours
#     "15m": 4*24*3,  # 3 days
#     "1h": 24*14,    # 14 days
#     "4h": 6*30,     # 30 days
#     "1d": 30*4      # 120 days
# }

INTERVAL_LIMIT_MAPPING: Final[dict[str, int]] = {
    "1m": 7*24*60,          # 7 days
    "15m": 60*24*4,         # 60 days
    "1h": 180*24,           # 180 days
    "4h": 365*6,            # 30 days
    "1d": 1100              # 1100 days
}

DEFAULT_TRAINING_CONFIG: dict[str, str | dict] = {
    "ticker": "BTCUSDT",
    "interval": "15m",
    "limit": 20000,
    "indicators_config": INDICATORS_CONFIG,
    "dt_features": True,
    "lag_features": {"lag_num": 3, "lag_cols": ['close_price', 'volume', 'rsi', 'macd', 'ema_20', 'sma_50']},
}

