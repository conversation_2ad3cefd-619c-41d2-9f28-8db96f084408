import pandas as pd
from collections import deque
from src.core.technical_analysis.data_structures.kline import KLine


class KLinesBuffer:
    def __init__(self, klines=None, max_size: int = 1000):
        self.max_size = max_size
        self.klines = deque(klines, maxlen=max_size) if klines else deque(maxlen=max_size)

    def append(self, kline: KLine):
        self.klines.append(kline)

    def pop(self, index: int = 0) -> KLine:
        return self.klines.pop() if index == -1 else self.klines.popleft()

    def get_last(self, n: int = None) -> KLine | list[KLine] | None:
        if not self.klines:
            return None
        return self.klines[-1] if n is None else list(self.klines)[-n:]

    def get_first(self, n: int = None) -> KLine | list[KLine] | None:
        if not self.klines:
            return None
        return self.klines[0] if n is None else list(self.klines)[:n]

    def to_dataframe(self) -> pd.DataFrame:
        return pd.DataFrame([k.dict() for k in self.klines])

    def clear(self):
        self.klines.clear()

    def __len__(self):
        return len(self.klines)

    def __repr__(self):
        return f"KLinesBuffer(size={len(self)}, max_size={self.max_size})"
