services:
  telegram-consumer:
    container_name: app-telegram-consumer
    build:
      context: ../../
      dockerfile: deploy/app/Dockerfile
    command: python apps/telegram_consumer.py
    environment:
      PYTHONPATH: "/app/"
      KAFKA_SERVERS: "kafka:9092"
      MINIO_SERVER: "minio:9000"
    env_file:
      - .env
    networks:
      - shared_network

networks:
  shared_network:
    name: app-shared-network
    external: true
