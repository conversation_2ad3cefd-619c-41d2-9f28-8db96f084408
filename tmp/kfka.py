from confluent_kafka import Producer
import json

producer = Producer({'bootstrap.servers': 'localhost:29092'})
test_alert = {
    "type": "volume_spike",
    "symbol": "BNBUSDT",
    "volume": "12345.67",
    "interval": "15m",
    "close_price": "450.25"
}
test_alert = {'type': 'ml_prediction', 'symbol': 'BNBUSDT', 'interval': '15m', 'prediction': '🟢 LONG', 'confidence': 0.51, 'threshold': 0.25}

producer.produce('signals', json.dumps(test_alert).encode('utf-8'))
producer.flush()