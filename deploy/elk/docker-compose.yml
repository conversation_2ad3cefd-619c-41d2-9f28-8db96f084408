services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.13.4 # Use specific recent version
    container_name: elasticsearch # Consider removing if you might run multiple instances during testing
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms1g -Xmx1g # Adjust memory as needed (Min 1g recommended)
      - xpack.security.enabled=false # Disable security for local dev ONLY
      - xpack.security.http.ssl.enabled=false
      - xpack.security.transport.ssl.enabled=false
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - shared_network # <-- CORRECTED: Use the internal network name defined below
    healthcheck:
      test: [ "CMD-SHELL", "curl -sf http://localhost:9200/_cluster/health | grep -vq '\"status\":\"red\"'" ] # Added -f to curl
      interval: 10s
      timeout: 5s
      retries: 10
    restart: unless-stopped

  logstash:
    image: docker.elastic.co/logstash/logstash:8.13.4 # Match ES/Kibana version
    container_name: logstash # Consider removing container_name
    command: logstash -f /usr/share/logstash/pipeline/logstash.conf
    volumes:
      # Mount the pipeline configuration file (relative to this docker-compose.yml)
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
      # Optional: Mount a volume for persistent queue if needed
      - logstash-data:/usr/share/logstash/data
    ports:
      # Port for Python's TCPLogstashHandler (from your Python code)
      - "5400:5400/tcp"
      # Port for Prefect logs (UDP)
      - "5000:5000/udp"
      # Optional: In case you need TCP on 5000 as well
      - "5000:5000/tcp"
      # Beats input (if needed in the future)
      - "5044:5044"
      # Logstash monitoring API (useful for debugging)
      - "9600:9600"
    depends_on: # Dependencies only work for services within THIS file
      elasticsearch:
        condition: service_healthy
    networks:
      - shared_network
    restart: unless-stopped

  kibana:
    image: docker.elastic.co/kibana/kibana:8.13.4 # Match ES/Logstash version
    container_name: kibana # Consider removing container_name
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200 # Connect to ES service name (works via shared network)
    ports:
      - "5601:5601"
    depends_on: # Dependencies only work for services within THIS file
      elasticsearch:
        condition: service_healthy
    networks:
      - shared_network
    restart: unless-stopped

networks:
  # Internal name used by services within this file
  shared_network:
    # Name of the actual Docker network created via 'docker network create'
    name: app-shared-network
    external: true

volumes: # Volumes are local to this compose project unless declared external
  elasticsearch-data:
  logstash-data: # Uncomment if needed for persistent queue