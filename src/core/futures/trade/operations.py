import logging
import traceback

from binance.error import ClientError
from src.binance_client import get_client


def open_long_limit(symbol: str, price: float, quantity: int, client_name: str = "um_futures"):
    client = get_client(client_name)
    response = client.new_order(
        symbol=symbol,
        side="BUY",
        type="LIMIT",
        quantity=quantity,
        timeInForce="GTC",
        price=price,
        positionSide="LONG"
    )
    return response


def open_short_limit(symbol: str, price: float, quantity: int, client_name: str = "um_futures"):
    client = get_client(client_name)
    response = client.new_order(
        symbol=symbol,
        side="SELL",
        type="LIMIT",
        quantity=quantity,
        timeInForce="GTC",
        price=price,
        positionSide="SHORT"
    )
    return response


def cancel_order(symbol: str, order_id: int, client_name: str = "um_futures"):
    client = get_client(client_name)
    response = client.cancel_order(
        symbol=symbol, orderId=order_id, recvWindow=2000
    )
    return response


def close_short_market(symbol: str, quantity: int, client_name: str = "um_futures"):
    """market close short position"""
    client = get_client(client_name)
    response = client.new_order(
        symbol=symbol,
        side="BUY",
        type="MARKET",
        quantity=quantity,
        positionSide="SHORT"
    )
    return response


def close_long_market(symbol: str, quantity: int, client_name: str = "um_futures"):
    """market close long position"""
    client = get_client(client_name)
    response = client.new_order(
        symbol=symbol,
        side="SELL",
        type="MARKET",
        quantity=quantity,
        positionSide="LONG"
    )
    return response


def close_short_limit(symbol: str, price: float, quantity: int, client_name: str = "um_futures"):
    """
    Close a SHORT position using a limit order.
    
    :param symbol: Trading pair symbol
    :param price: Limit price for the order
    :param quantity: Quantity to close
    :param client_name: Name of the client to use, defaults to "um_futures"
    :return: Response from the Binance API
    """
    client = get_client(client_name)
    response = client.new_order(
        symbol=symbol,
        side="BUY",
        type="LIMIT",
        quantity=quantity,
        timeInForce="GTC",
        price=price,
        positionSide="SHORT"
    )
    return response


def close_long_limit(symbol: str, price: float, quantity: int, client_name: str = "um_futures"):
    """
    Close a LONG position using a limit order.
    
    :param symbol: Trading pair symbol
    :param price: Limit price for the order
    :param quantity: Quantity to close
    :param client_name: Name of the client to use, defaults to "um_futures"
    :return: Response from the Binance API
    """
    client = get_client(client_name)
    response = client.new_order(
        symbol=symbol,
        side="SELL",
        type="LIMIT",
        quantity=quantity,
        timeInForce="GTC",
        price=price,
        positionSide="LONG"
    )
    return response


def tp_sl_exist_long(ticker, tp_price: float, sl_price: float, client_name="um_futures"):
    client = get_client(client_name)
    tp_response = client.new_order(
        symbol=ticker,
        side="SELL",
        positionSide="LONG",
        type="TAKE_PROFIT_MARKET",
        timeInForce="GTE_GTC",
        stopprice=tp_price,
        closePosition=True
    )
    sl_response = client.new_order(
        symbol=ticker,
        side="SELL",
        positionSide="LONG",
        type="STOP_MARKET",
        timeInForce="GTE_GTC",
        stopprice=sl_price,
        closePosition=True
    )
    return [tp_response, sl_response]


def execute_order(action, ticker, price, quantity, parsed_response):
    try:
        action(ticker, price, abs(quantity))
        logging.info(f"{action.__name__.replace('_', ' ').title()} order created", extra=parsed_response)
        return True
    except ClientError as e:
        # Add the traceback to the extra dictionary
        parsed_response['traceback'] = traceback.format_exc()
        logging.error(f"Failed to create {action.__name__.replace('_', ' ').title()} order", extra=parsed_response)
        return False


def handle_choice(choice: int, ticker: str, order: dict, parsed_response: dict, open_orders: list[dict]):
    actions = {
        1: (open_long_limit, "Open LONG"),
        2: (open_short_limit, "Open SHORT"),
        3: (close_long_limit, "Close LONG"),
        4: (close_short_limit, "Close SHORT"),
        5: (cancel_order, "Canceled")
    }
    kwargs = {**order, 'symbol': ticker, 'order_id': open_orders[0]['order_id']}  # todo: finish this

    exec_result = True
    if choice in actions:
        if choice == 5:
            actions[choice][0](ticker, open_orders[0]['order_id'])
            logging.info(f"{actions[choice][1]} order", extra=parsed_response)
        elif order:
            exec_result = execute_order(actions[choice][0], ticker, order['price'], order['quantity'], parsed_response)
        else:
            logging.error(f"Failed to execute choice={choice}", extra=parsed_response)
            exec_result = False
    else:
        logging.info("Do nothing", extra=parsed_response)

    return exec_result
