import os
import async<PERSON>
import json

from io import By<PERSON><PERSON>

from confluent_kafka import Consumer
from telegram import Bo<PERSON>, InputMediaDocument

from src.s3_client import s3client
from src.config.base import TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, KAFKA_SERVERS, KAFKA_TOPIC, MINIO_BUCKET
from src.config.logs import setup_logging
from src.core.telegram.alerting import AlertFormatterRegistry

logger = setup_logging()


async def send_telegram_message(bot: Bo<PERSON>, text: str, image_names: str | list[str] = None):
    # Handle both single string and list inputs
    if image_names is not None and not isinstance(image_names, list):
        image_names = [image_names]

    if not image_names:
        # No images, just send text
        await bot.send_message(
            chat_id=TELEGRAM_CHAT_ID,
            text=text,
            parse_mode='HTML'
        )
        return

    if len(image_names) == 1:
        # Single image - send as document with caption
        buffer = BytesIO()
        s3client().download_fileobj(MINIO_BUCKET, image_names[0], buffer)
        buffer.seek(0)
        filename = os.path.basename(image_names[0])

        await bot.send_document(
            chat_id=TELEGRAM_CHAT_ID,
            document=buffer,
            filename=filename,
            caption=text,
            parse_mode='HTML'
        )
    else:
        # Multiple images - send as media group
        media_group = []

        for i, image_name in enumerate(image_names):
            buffer = BytesIO()
            s3client().download_fileobj(MINIO_BUCKET, image_name, buffer)
            buffer.seek(0)
            buffer.name = os.path.basename(image_name)  # Important: set the name attribute

            # Only the first document gets the caption
            media = InputMediaDocument(
                media=buffer,
                caption=text if i == 0 else None,
                parse_mode='HTML' if i == 0 else None
            )
            media_group.append(media)

        await bot.send_media_group(
            chat_id=TELEGRAM_CHAT_ID,
            media=media_group
        )


async def consume_alerts():
    consumer = Consumer({
        'bootstrap.servers': KAFKA_SERVERS,
        'group.id': 'telegram_bot_group',
        'auto.offset.reset': 'latest'
    })
    consumer.subscribe([KAFKA_TOPIC])
    formatter_registry = AlertFormatterRegistry()

    bot = Bot(token=TELEGRAM_BOT_TOKEN)
    logger.info("Telegram consumer started, listening for alerts...")

    try:
        while True:
            msg = consumer.poll(1.0)
            if msg is None:
                continue
            if msg.error():
                logger.error(f"Kafka consumer error: {msg.error()}")
                continue

            alert = json.loads(msg.value().decode('utf-8'))
            message = formatter_registry.format(alert)
            image_path = alert.get("path")

            logger.info(f"Sending Telegram message: {message}" + f" (image: {image_path})" * bool(image_path))
            await send_telegram_message(bot, message, image_path)

    except asyncio.CancelledError:
        logger.info("Consumer task cancelled.")
    except KeyboardInterrupt:
        logger.info("Consumer stopped by user.")
    finally:
        consumer.close()


if __name__ == '__main__':
    asyncio.run(consume_alerts())